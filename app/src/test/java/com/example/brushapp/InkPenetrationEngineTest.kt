package com.example.brushapp

import org.junit.Test
import org.junit.Assert.*
import kotlin.math.abs

/**
 * 墨水渗透引擎单元测试
 * 验证物理模型的正确性和数值稳定性
 */
class InkPenetrationEngineTest {

    private val engine = InkPenetrationEngine()
    private val tolerance = 0.001f

    @Test
    fun testPenetrationPointCreation() {
        // 测试渗透点创建
        val point = engine.startPenetration(10f, 20f, 1.0f)
        
        assertEquals(10f, point.x, tolerance)
        assertEquals(20f, point.y, tolerance)
        assertEquals(1.0f, point.concentration, tolerance)
        assertTrue(point.isActive)
        assertTrue(point.radius > 0f)
    }

    @Test
    fun testConcentrationCalculation() {
        // 创建渗透点
        val point = engine.startPenetration(0f, 0f, 1.0f)
        
        // 测试中心点浓度
        val centerConcentration = engine.calculateConcentrationAt(0f, 0f)
        assertTrue("中心点浓度应该最高", centerConcentration > 0.5f)
        
        // 测试边缘点浓度
        val edgeConcentration = engine.calculateConcentrationAt(100f, 100f)
        assertTrue("远距离点浓度应该很低", edgeConcentration < 0.1f)
        
        // 测试距离衰减
        val nearConcentration = engine.calculateConcentrationAt(1f, 1f)
        val farConcentration = engine.calculateConcentrationAt(10f, 10f)
        assertTrue("距离越远浓度越低", nearConcentration > farConcentration)
    }

    @Test
    fun testWidthMultiplierCalculation() {
        // 创建渗透点
        engine.startPenetration(0f, 0f, 1.0f)
        
        // 测试线宽倍数计算
        val baseWidth = 2.0f
        val multiplier = engine.calculateWidthMultiplier(0f, 0f, baseWidth)
        
        assertTrue("线宽倍数应该大于1", multiplier >= 1.0f)
        assertTrue("线宽倍数应该有合理上限", multiplier <= 10.0f)
    }

    @Test
    fun testPenetrationUpdate() {
        // 创建渗透点
        val point = engine.startPenetration(0f, 0f, 1.0f)
        val initialRadius = point.radius
        val initialConcentration = point.concentration
        
        // 模拟时间推进
        val currentTime = System.nanoTime()
        Thread.sleep(100) // 等待100ms
        engine.updatePenetration(System.nanoTime())
        
        // 验证渗透点状态变化
        assertTrue("半径应该增长", point.radius > initialRadius)
        assertTrue("浓度应该衰减", point.concentration <= initialConcentration)
    }

    @Test
    fun testMultiplePenetrationPoints() {
        // 创建多个渗透点
        val point1 = engine.startPenetration(0f, 0f, 1.0f)
        val point2 = engine.startPenetration(10f, 10f, 0.8f)
        
        assertEquals(2, engine.getActivePenetrationCount())
        
        // 测试叠加效应
        val midpointConcentration = engine.calculateConcentrationAt(5f, 5f)
        assertTrue("中间点应该受到两个渗透点的影响", midpointConcentration > 0f)
    }

    @Test
    fun testPenetrationCleanup() {
        // 创建渗透点
        engine.startPenetration(0f, 0f, 1.0f)
        assertEquals(1, engine.getActivePenetrationCount())
        
        // 清理所有渗透点
        engine.clearAll()
        assertEquals(0, engine.getActivePenetrationCount())
        
        // 验证浓度为0
        val concentration = engine.calculateConcentrationAt(0f, 0f)
        assertEquals(0f, concentration, tolerance)
    }

    @Test
    fun testPhysicalConstants() {
        // 验证物理常数的合理性
        assertTrue("扩散系数应该为正", InkPenetrationEngine.DIFFUSION_COEFFICIENT > 0f)
        assertTrue("表面张力应该为正", InkPenetrationEngine.SURFACE_TENSION > 0f)
        assertTrue("纸张孔隙率应该在0-1之间", 
                  InkPenetrationEngine.PAPER_POROSITY > 0f && 
                  InkPenetrationEngine.PAPER_POROSITY < 1f)
        assertTrue("墨水粘度应该为正", InkPenetrationEngine.INK_VISCOSITY > 0f)
    }

    @Test
    fun testNumericalStability() {
        // 测试数值稳定性
        val point = engine.startPenetration(0f, 0f, 1.0f)
        
        // 进行多次更新
        val currentTime = System.nanoTime()
        for (i in 1..100) {
            engine.updatePenetration(currentTime + i * 16_000_000L) // 模拟60fps
        }
        
        // 验证数值没有发散
        assertTrue("浓度应该保持在合理范围", point.concentration >= 0f && point.concentration <= 2f)
        assertTrue("半径应该保持在合理范围", point.radius >= 0f && point.radius <= 1000f)
        assertTrue("渗透深度应该保持在合理范围", point.penetrationDepth >= 0f && point.penetrationDepth <= 100f)
    }

    @Test
    fun testBoundaryConditions() {
        // 测试边界条件
        
        // 零强度渗透
        val zeroPoint = engine.startPenetration(0f, 0f, 0f)
        assertTrue("零强度渗透应该产生很小的效果", zeroPoint.concentration <= 0.1f)
        
        // 最大强度渗透
        val maxPoint = engine.startPenetration(0f, 0f, 10f)
        assertTrue("高强度渗透应该被限制", maxPoint.concentration <= 2f)
        
        // 负坐标
        val negativePoint = engine.startPenetration(-10f, -10f, 1f)
        assertTrue("负坐标应该正常工作", negativePoint.isActive)
    }

    @Test
    fun testPerformance() {
        // 性能测试：创建多个渗透点并更新
        val startTime = System.currentTimeMillis()
        
        // 创建最大数量的渗透点
        for (i in 0 until 10) {
            engine.startPenetration(i * 10f, i * 10f, 1f)
        }
        
        // 进行多次更新
        val currentTime = System.nanoTime()
        for (i in 1..60) { // 模拟1秒的更新
            engine.updatePenetration(currentTime + i * 16_000_000L)
        }
        
        val endTime = System.currentTimeMillis()
        val duration = endTime - startTime
        
        // 验证性能在可接受范围内（应该在几毫秒内完成）
        assertTrue("性能测试应该在合理时间内完成", duration < 100)
    }
}
