package com.example.brushapp

import kotlin.math.*

/**
 * 墨水渗透引擎 - 基于真实物理模型的墨水扩散模拟
 * 
 * 基于以下物理原理：
 * 1. Fick扩散定律 - 浓度梯度驱动的扩散
 * 2. 毛细作用 - 纸张纤维的毛细管效应
 * 3. 达西定律 - 多孔介质中的流体流动
 * 4. 表面张力 - 影响墨水边界形状
 */
class InkPenetrationEngine {
    
    companion object {
        // 物理常数 - 强化渗透效果
        const val DIFFUSION_COEFFICIENT = 4.0f      // 扩散系数 (大幅增强扩散速度)
        const val CAPILLARY_LENGTH = 2.5f           // 毛细长度 (mm)
        const val SURFACE_TENSION = 0.072f          // 表面张力 (N/m)
        const val CONTACT_ANGLE = 45f               // 接触角 (度)

        // 纸张特性
        const val PAPER_POROSITY = 0.9f             // 纸张孔隙率 (进一步增加孔隙率)
        const val FIBER_RADIUS = 0.01f              // 纤维半径 (mm)
        const val PERMEABILITY = 4.0f               // 渗透率 (大幅增强渗透性)
        const val ABSORPTION_RATE = 0.05f           // 吸收速率 (大幅降低吸收，延长效果)

        // 墨水特性
        const val INK_VISCOSITY = 0.001f            // 墨水粘度 (Pa·s)
        const val INK_DENSITY = 1000f               // 墨水密度 (kg/m³)
        const val INITIAL_CONCENTRATION = 4.0f      // 初始浓度 (大幅增强初始效果)

        // 数值计算参数
        const val GRID_SIZE = 0.5f                  // 网格大小 (像素)
        const val TIME_STEP = 0.016f                // 时间步长 (s, 60fps)
        const val MAX_PENETRATION_RADIUS = 120f     // 最大渗透半径 (进一步扩大影响范围)
    }
    
    /**
     * 渗透点数据结构
     * 表示墨水在某个位置的渗透状态
     */
    data class PenetrationPoint(
        val x: Float,                               // 位置X
        val y: Float,                               // 位置Y
        val startTime: Long,                        // 开始时间
        var concentration: Float,                   // 当前浓度
        var radius: Float,                          // 当前渗透半径
        var penetrationDepth: Float,                // 渗透深度
        var isActive: Boolean = true                // 是否活跃
    )
    
    /**
     * 纸张区域特性
     */
    data class PaperRegion(
        val porosity: Float = PAPER_POROSITY,      // 局部孔隙率
        val roughness: Float = 1.0f,               // 表面粗糙度
        val absorptionRate: Float = ABSORPTION_RATE // 局部吸收率
    )
    
    // 活跃的渗透点列表
    private val penetrationPoints = mutableListOf<PenetrationPoint>()
    
    // 纸张特性映射（可以模拟不同区域的纸张特性）
    private val paperRegions = mutableMapOf<Pair<Int, Int>, PaperRegion>()
    
    /**
     * 开始新的墨水渗透
     */
    fun startPenetration(x: Float, y: Float, initialIntensity: Float = 1.0f): PenetrationPoint {
        val point = PenetrationPoint(
            x = x,
            y = y,
            startTime = System.nanoTime(),
            concentration = initialIntensity * INITIAL_CONCENTRATION,
            radius = 20f, // 增加初始半径到20像素，确保能覆盖附近的查询点
            penetrationDepth = 0f
        )
        
        synchronized(penetrationPoints) {
            penetrationPoints.add(point)
        }
        return point
    }
    
    /**
     * 更新所有渗透点的状态
     */
    fun updatePenetration(currentTime: Long) {
        // 创建列表副本以避免并发修改异常
        val pointsCopy = synchronized(penetrationPoints) {
            ArrayList(penetrationPoints)
        }

        // 用于收集需要移除的点
        val pointsToRemove = mutableListOf<PenetrationPoint>()

        for (point in pointsCopy) {
            if (!point.isActive) {
                pointsToRemove.add(point)
                continue
            }

            val deltaTime = (currentTime - point.startTime) / 1_000_000_000f
            updateSinglePoint(point, deltaTime)

            // 清理浓度过低或半径过大的点
            if (point.concentration < 0.001f || point.radius > MAX_PENETRATION_RADIUS) {
                point.isActive = false
                pointsToRemove.add(point)
            }
        }

        // 安全地移除非活跃点
        if (pointsToRemove.isNotEmpty()) {
            synchronized(penetrationPoints) {
                penetrationPoints.removeAll(pointsToRemove)
            }
        }
    }
    
    /**
     * 更新单个渗透点
     * 基于扩散方程和毛细作用
     */
    private fun updateSinglePoint(point: PenetrationPoint, deltaTime: Float) {
        // 检查输入参数
        if (deltaTime <= 0f || !point.concentration.isFinite() || !point.radius.isFinite()) {
            android.util.Log.w("InkPenetration",
                "⚠️ updateSinglePoint输入异常: deltaTime=$deltaTime, concentration=${point.concentration}, radius=${point.radius}")
            return
        }

        // 获取局部纸张特性
        val paperRegion = getPaperRegion(point.x, point.y)

        // 1. 径向扩散 (基于Fick定律) - 添加安全检查
        val diffusionRate = DIFFUSION_COEFFICIENT * paperRegion.porosity
        val diffusionParam = 2 * diffusionRate * deltaTime
        if (diffusionParam < 0f) {
            android.util.Log.w("InkPenetration", "⚠️ 扩散参数为负: $diffusionParam")
            return
        }
        val radialGrowth = sqrt(diffusionParam).coerceIn(0f, 10f) // 限制增长范围

        // 2. 毛细作用增强 - 添加安全检查
        val capillaryEffect = calculateCapillaryEffect(point, paperRegion).coerceIn(-0.5f, 2f)
        val enhancedGrowth = radialGrowth * (1f + capillaryEffect)

        // 3. 更新半径 - 添加合理范围限制
        point.radius = (point.radius + enhancedGrowth).coerceIn(1f, MAX_PENETRATION_RADIUS)

        // 4. 浓度衰减 - 简化计算避免NaN
        val absorptionDecay = exp(-ABSORPTION_RATE * deltaTime).coerceIn(0.1f, 1f)
        val diffusionDecay = 0.99f // 简化的扩散衰减，避免复杂计算

        point.concentration = (point.concentration * diffusionDecay * absorptionDecay).coerceAtLeast(0.001f)

        // 5. 渗透深度 - 简化计算
        point.penetrationDepth += 0.1f * deltaTime // 简化的深度增长

        // 最终安全检查
        if (!point.concentration.isFinite() || !point.radius.isFinite()) {
            android.util.Log.e("InkPenetration",
                "❌ updateSinglePoint产生NaN: concentration=${point.concentration}, radius=${point.radius}")
            point.concentration = 0.001f
            point.radius = 20f
        }
    }
    
    /**
     * 计算毛细作用效应
     */
    private fun calculateCapillaryEffect(point: PenetrationPoint, paperRegion: PaperRegion): Float {
        // 简化的毛细作用计算，避免复杂的数学运算
        return (paperRegion.roughness * 0.5f).coerceIn(0f, 1f)
    }
    
    /**
     * 获取指定位置的纸张特性
     */
    private fun getPaperRegion(x: Float, y: Float): PaperRegion {
        val gridX = (x / 10f).toInt() // 10像素为一个纸张区域
        val gridY = (y / 10f).toInt()
        
        return paperRegions.getOrPut(Pair(gridX, gridY)) {
            // 生成随机的纸张特性变化，模拟纸张的不均匀性
            val porosityVariation = (Math.random().toFloat() - 0.5f) * 0.2f
            val roughnessVariation = (Math.random().toFloat() - 0.5f) * 0.3f
            val absorptionVariation = (Math.random().toFloat() - 0.5f) * 0.1f
            
            PaperRegion(
                porosity = (PAPER_POROSITY + porosityVariation).coerceIn(0.3f, 0.9f),
                roughness = (1.0f + roughnessVariation).coerceIn(0.7f, 1.5f),
                absorptionRate = (ABSORPTION_RATE + absorptionVariation).coerceIn(0.1f, 0.5f)
            )
        }
    }
    
    /**
     * 计算指定位置的墨水浓度
     * 考虑所有活跃渗透点的贡献
     */
    fun calculateConcentrationAt(x: Float, y: Float): Float {
        var totalConcentration = 0f
        var pointsSize = 0

        // 创建列表副本以避免并发修改异常
        val pointsCopy = synchronized(penetrationPoints) {
            pointsSize = penetrationPoints.size
            ArrayList(penetrationPoints)
        }

        for (point in pointsCopy) {
            if (!point.isActive) continue

            val distance = sqrt((x - point.x).pow(2) + (y - point.y).pow(2))

            // 详细的距离和贡献计算日志
            android.util.Log.d("InkPenetration",
                "🎯 渗透点检查: 点(${String.format("%.1f", point.x)}, ${String.format("%.1f", point.y)}) " +
                "到查询点(${String.format("%.1f", x)}, ${String.format("%.1f", y)}) " +
                "距离=${String.format("%.2f", distance)}, 半径=${String.format("%.2f", point.radius)}, " +
                "浓度=${String.format("%.4f", point.concentration)}")

            if (distance <= point.radius) {
                // 高斯分布的浓度衰减
                val normalizedDistance = distance / point.radius
                val gaussianDecay = exp(-normalizedDistance * normalizedDistance * 2f)
                val contribution = point.concentration * gaussianDecay

                totalConcentration += contribution

                android.util.Log.d("InkPenetration",
                    "✅ 贡献浓度: 标准化距离=${String.format("%.4f", normalizedDistance)}, " +
                    "衰减=${String.format("%.4f", gaussianDecay)}, " +
                    "贡献=${String.format("%.4f", contribution)}")
            } else {
                android.util.Log.d("InkPenetration",
                    "❌ 超出范围: 距离${String.format("%.2f", distance)} > 半径${String.format("%.2f", point.radius)}")
            }
        }

        val finalConcentration = totalConcentration.coerceIn(0f, 10f) // 增加最大浓度限制

        // 详细的浓度计算日志 - 显示所有活跃点的贡献
        android.util.Log.d("InkPenetration",
            "💧 浓度计算: 位置(${String.format("%.1f", x)}, ${String.format("%.1f", y)}) → " +
            "活跃点=$pointsSize, " +
            "原始总浓度=${String.format("%.4f", totalConcentration)}, " +
            "最终浓度=${String.format("%.4f", finalConcentration)}")

        // 如果有活跃点但浓度为0，这是个问题
        if (pointsSize > 0 && finalConcentration == 0f) {
            android.util.Log.w("InkPenetration", "⚠️ 警告: 有${pointsSize}个活跃点但浓度为0!")
        }

        return finalConcentration
    }
    
    /**
     * 计算指定位置的线宽增量 - 优化用户体验
     */
    fun calculateWidthMultiplier(x: Float, y: Float, baseWidth: Float): Float {
        val concentration = calculateConcentrationAt(x, y)

        // 使用更激进的响应曲线，让小浓度也有明显效果
        val sensitiveResponse = concentration.pow(0.3f) // 更敏感的响应曲线
        val concentrationEffect = sensitiveResponse * (2.5f - sensitiveResponse * 0.5f) // 更激进的增长曲线

        // 最大增量为基础线宽的15倍，极明显的效果
        val maxMultiplier = 15f
        val multiplier = 1f + concentrationEffect * (maxMultiplier - 1f)

        // 详细的线宽计算日志 - 显示所有计算步骤
        android.util.Log.d("InkPenetration",
            "🧮 线宽计算详情: 位置(${String.format("%.1f", x)}, ${String.format("%.1f", y)}) → " +
            "浓度=${String.format("%.4f", concentration)}, " +
            "响应=${String.format("%.4f", sensitiveResponse)}, " +
            "效果=${String.format("%.4f", concentrationEffect)}, " +
            "最终倍数=${String.format("%.4f", multiplier)}")

        return multiplier
    }
    
    /**
     * 获取活跃渗透点数量
     */
    fun getActivePenetrationCount(): Int {
        return synchronized(penetrationPoints) {
            penetrationPoints.count { it.isActive }
        }
    }
    
    /**
     * 清理所有渗透点
     */
    fun clearAll() {
        synchronized(penetrationPoints) {
            penetrationPoints.clear()
        }
        paperRegions.clear()
    }
    
    /**
     * 获取调试信息
     */
    fun getDebugInfo(): String {
        val activeCount = getActivePenetrationCount()
        val avgConcentration = if (activeCount > 0) {
            synchronized(penetrationPoints) {
                penetrationPoints.filter { it.isActive }.map { it.concentration }.average().toFloat()
            }
        } else 0f

        return "渗透点: $activeCount, 平均浓度: %.3f".format(avgConcentration)
    }
}
