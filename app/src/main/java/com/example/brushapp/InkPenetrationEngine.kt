package com.example.brushapp

import kotlin.math.*

/**
 * 墨水渗透引擎 - 基于真实物理模型的墨水扩散模拟
 * 
 * 基于以下物理原理：
 * 1. Fick扩散定律 - 浓度梯度驱动的扩散
 * 2. 毛细作用 - 纸张纤维的毛细管效应
 * 3. 达西定律 - 多孔介质中的流体流动
 * 4. 表面张力 - 影响墨水边界形状
 */
class InkPenetrationEngine {
    
    companion object {
        // 物理常数 - 强化渗透效果
        const val DIFFUSION_COEFFICIENT = 4.0f      // 扩散系数 (大幅增强扩散速度)
        const val CAPILLARY_LENGTH = 2.5f           // 毛细长度 (mm)
        const val SURFACE_TENSION = 0.072f          // 表面张力 (N/m)
        const val CONTACT_ANGLE = 45f               // 接触角 (度)

        // 纸张特性
        const val PAPER_POROSITY = 0.9f             // 纸张孔隙率 (进一步增加孔隙率)
        const val FIBER_RADIUS = 0.01f              // 纤维半径 (mm)
        const val PERMEABILITY = 4.0f               // 渗透率 (大幅增强渗透性)
        const val ABSORPTION_RATE = 0.1f            // 吸收速率 (进一步降低吸收，延长效果)

        // 墨水特性
        const val INK_VISCOSITY = 0.001f            // 墨水粘度 (Pa·s)
        const val INK_DENSITY = 1000f               // 墨水密度 (kg/m³)
        const val INITIAL_CONCENTRATION = 4.0f      // 初始浓度 (大幅增强初始效果)

        // 数值计算参数
        const val GRID_SIZE = 0.5f                  // 网格大小 (像素)
        const val TIME_STEP = 0.016f                // 时间步长 (s, 60fps)
        const val MAX_PENETRATION_RADIUS = 120f     // 最大渗透半径 (进一步扩大影响范围)
    }
    
    /**
     * 渗透点数据结构
     * 表示墨水在某个位置的渗透状态
     */
    data class PenetrationPoint(
        val x: Float,                               // 位置X
        val y: Float,                               // 位置Y
        val startTime: Long,                        // 开始时间
        var concentration: Float,                   // 当前浓度
        var radius: Float,                          // 当前渗透半径
        var penetrationDepth: Float,                // 渗透深度
        var isActive: Boolean = true                // 是否活跃
    )
    
    /**
     * 纸张区域特性
     */
    data class PaperRegion(
        val porosity: Float = PAPER_POROSITY,      // 局部孔隙率
        val roughness: Float = 1.0f,               // 表面粗糙度
        val absorptionRate: Float = ABSORPTION_RATE // 局部吸收率
    )
    
    // 活跃的渗透点列表
    private val penetrationPoints = mutableListOf<PenetrationPoint>()
    
    // 纸张特性映射（可以模拟不同区域的纸张特性）
    private val paperRegions = mutableMapOf<Pair<Int, Int>, PaperRegion>()
    
    /**
     * 开始新的墨水渗透
     */
    fun startPenetration(x: Float, y: Float, initialIntensity: Float = 1.0f): PenetrationPoint {
        val point = PenetrationPoint(
            x = x,
            y = y,
            startTime = System.nanoTime(),
            concentration = initialIntensity * INITIAL_CONCENTRATION,
            radius = GRID_SIZE,
            penetrationDepth = 0f
        )
        
        synchronized(penetrationPoints) {
            penetrationPoints.add(point)
        }
        return point
    }
    
    /**
     * 更新所有渗透点的状态
     */
    fun updatePenetration(currentTime: Long) {
        // 创建列表副本以避免并发修改异常
        val pointsCopy = synchronized(penetrationPoints) {
            ArrayList(penetrationPoints)
        }

        // 用于收集需要移除的点
        val pointsToRemove = mutableListOf<PenetrationPoint>()

        for (point in pointsCopy) {
            if (!point.isActive) {
                pointsToRemove.add(point)
                continue
            }

            val deltaTime = (currentTime - point.startTime) / 1_000_000_000f
            updateSinglePoint(point, deltaTime)

            // 清理浓度过低或半径过大的点
            if (point.concentration < 0.01f || point.radius > MAX_PENETRATION_RADIUS) {
                point.isActive = false
                pointsToRemove.add(point)
            }
        }

        // 安全地移除非活跃点
        if (pointsToRemove.isNotEmpty()) {
            synchronized(penetrationPoints) {
                penetrationPoints.removeAll(pointsToRemove)
            }
        }
    }
    
    /**
     * 更新单个渗透点
     * 基于扩散方程和毛细作用
     */
    private fun updateSinglePoint(point: PenetrationPoint, deltaTime: Float) {
        // 获取局部纸张特性
        val paperRegion = getPaperRegion(point.x, point.y)
        
        // 1. 径向扩散 (基于Fick定律)
        val diffusionRate = DIFFUSION_COEFFICIENT * paperRegion.porosity
        val radialGrowth = sqrt(2 * diffusionRate * deltaTime)
        
        // 2. 毛细作用增强
        val capillaryEffect = calculateCapillaryEffect(point, paperRegion)
        val enhancedGrowth = radialGrowth * (1f + capillaryEffect)
        
        // 3. 更新半径
        point.radius += enhancedGrowth
        
        // 4. 浓度衰减 (基于扩散和吸收)
        val volumeRatio = (point.radius * point.radius) / (GRID_SIZE * GRID_SIZE)
        val diffusionDecay = 1f / sqrt(volumeRatio)
        val absorptionDecay = exp(-paperRegion.absorptionRate * deltaTime)
        
        point.concentration *= diffusionDecay * absorptionDecay
        
        // 5. 渗透深度 (基于达西定律)
        val pressureGradient = point.concentration * INK_DENSITY * 9.8f // 重力压力
        val darcyVelocity = (PERMEABILITY * pressureGradient) / INK_VISCOSITY
        point.penetrationDepth += darcyVelocity * deltaTime
    }
    
    /**
     * 计算毛细作用效应
     */
    private fun calculateCapillaryEffect(point: PenetrationPoint, paperRegion: PaperRegion): Float {
        // Lucas-Washburn方程的简化版本
        val contactAngleRad = Math.toRadians(CONTACT_ANGLE.toDouble()).toFloat()
        val cosContactAngle = cos(contactAngleRad)
        
        // 毛细压力
        val capillaryPressure = (2 * SURFACE_TENSION * cosContactAngle) / FIBER_RADIUS
        
        // 毛细速度
        val capillaryVelocity = (capillaryPressure * FIBER_RADIUS * FIBER_RADIUS) / 
                               (8 * INK_VISCOSITY * point.penetrationDepth.coerceAtLeast(FIBER_RADIUS))
        
        // 转换为增强因子
        return (capillaryVelocity * paperRegion.roughness).coerceIn(0f, 2f)
    }
    
    /**
     * 获取指定位置的纸张特性
     */
    private fun getPaperRegion(x: Float, y: Float): PaperRegion {
        val gridX = (x / 10f).toInt() // 10像素为一个纸张区域
        val gridY = (y / 10f).toInt()
        
        return paperRegions.getOrPut(Pair(gridX, gridY)) {
            // 生成随机的纸张特性变化，模拟纸张的不均匀性
            val porosityVariation = (Math.random().toFloat() - 0.5f) * 0.2f
            val roughnessVariation = (Math.random().toFloat() - 0.5f) * 0.3f
            val absorptionVariation = (Math.random().toFloat() - 0.5f) * 0.1f
            
            PaperRegion(
                porosity = (PAPER_POROSITY + porosityVariation).coerceIn(0.3f, 0.9f),
                roughness = (1.0f + roughnessVariation).coerceIn(0.7f, 1.5f),
                absorptionRate = (ABSORPTION_RATE + absorptionVariation).coerceIn(0.1f, 0.5f)
            )
        }
    }
    
    /**
     * 计算指定位置的墨水浓度
     * 考虑所有活跃渗透点的贡献
     */
    fun calculateConcentrationAt(x: Float, y: Float): Float {
        var totalConcentration = 0f
        var pointsSize = 0

        // 创建列表副本以避免并发修改异常
        val pointsCopy = synchronized(penetrationPoints) {
            pointsSize = penetrationPoints.size
            ArrayList(penetrationPoints)
        }

        for (point in pointsCopy) {
            if (!point.isActive) continue

            val distance = sqrt((x - point.x).pow(2) + (y - point.y).pow(2))

            if (distance <= point.radius) {
                // 高斯分布的浓度衰减
                val normalizedDistance = distance / point.radius
                val gaussianDecay = exp(-normalizedDistance * normalizedDistance * 2f)

                totalConcentration += point.concentration * gaussianDecay
            }
        }

        val finalConcentration = totalConcentration.coerceIn(0f, 10f) // 增加最大浓度限制

        // 调试日志 - 降低频率
        if (finalConcentration > 0.5f) { // 只在浓度较高时输出
            android.util.Log.d("InkPenetration",
                "位置($x, $y): 活跃点=$pointsSize, 总浓度=$finalConcentration")
        }

        return finalConcentration
    }
    
    /**
     * 计算指定位置的线宽增量 - 优化用户体验
     */
    fun calculateWidthMultiplier(x: Float, y: Float, baseWidth: Float): Float {
        val concentration = calculateConcentrationAt(x, y)

        // 使用更激进的响应曲线，让小浓度也有明显效果
        val sensitiveResponse = concentration.pow(0.3f) // 更敏感的响应曲线
        val concentrationEffect = sensitiveResponse * (2.5f - sensitiveResponse * 0.5f) // 更激进的增长曲线

        // 最大增量为基础线宽的15倍，极明显的效果
        val maxMultiplier = 15f
        val multiplier = 1f + concentrationEffect * (maxMultiplier - 1f)

        // 调试日志 - 降低频率
        if (concentration > 0.01f && multiplier > 1.1f) { // 进一步降低阈值，更容易看到效果
            android.util.Log.d("InkPenetration", "线宽计算: 位置($x, $y), 浓度=$concentration, 响应=$sensitiveResponse, 效果=$concentrationEffect, 倍数=$multiplier")
        }

        return multiplier
    }
    
    /**
     * 获取活跃渗透点数量
     */
    fun getActivePenetrationCount(): Int {
        return synchronized(penetrationPoints) {
            penetrationPoints.count { it.isActive }
        }
    }
    
    /**
     * 清理所有渗透点
     */
    fun clearAll() {
        synchronized(penetrationPoints) {
            penetrationPoints.clear()
        }
        paperRegions.clear()
    }
    
    /**
     * 获取调试信息
     */
    fun getDebugInfo(): String {
        val activeCount = getActivePenetrationCount()
        val avgConcentration = if (activeCount > 0) {
            synchronized(penetrationPoints) {
                penetrationPoints.filter { it.isActive }.map { it.concentration }.average().toFloat()
            }
        } else 0f

        return "渗透点: $activeCount, 平均浓度: %.3f".format(avgConcentration)
    }
}
