package com.example.brushapp

/**
 * 笔刷类型枚举
 * 定义不同类型的笔刷及其特性
 */
enum class BrushType(
    val displayName: String,
    val minWidth: Float,
    val maxWidth: Float,
    val pressureSensitivity: Float,
    val hasInkFlow: Boolean = false,
    val hasAdvancedTip: Boolean = false
) {
    /**
     * 普通笔刷 - 适合日常书写
     */
    NORMAL(
        displayName = "普通笔",
        minWidth = 2f,
        maxWidth = 8f,
        pressureSensitivity = 1.2f,
        hasInkFlow = false,
        hasAdvancedTip = true
    ),
    
    /**
     * 秀丽笔 - 模拟真实秀丽笔效果
     * 特点：压感敏感度高，线条粗细变化大，具有墨水流动效果
     */
    CALLIGRAPHY_PEN(
        displayName = "秀丽笔",
        minWidth = 0.8f,      // 最细线条，模拟笔尖
        maxWidth = 15f,       // 最粗线条，模拟笔身
        pressureSensitivity = 2.5f,  // 高压感敏感度
        hasInkFlow = true,    // 启用墨水流动效果
        hasAdvancedTip = true // 启用高级笔锋效果
    ),

    /**
     * 墨水渗透笔 - 基于真实物理模型的墨水渗透模拟
     * 特点：完全基于Fick扩散定律、Lucas-Washburn毛细作用和达西流动的物理模拟
     */
    INK_PENETRATION(
        displayName = "渗透笔",
        minWidth = 0.8f,      // 固定的基础线宽，不受压感影响
        maxWidth = 20f,       // 物理模型最大线宽（仅用于参考）
        pressureSensitivity = 0f,    // 完全不受压感影响
        hasInkFlow = true,    // 使用物理墨水流动模型
        hasAdvancedTip = true // 使用物理笔锋效果
    );
    
    /**
     * 获取基于压感的线条宽度
     * @param pressure 压感值 (0.0 - 1.0)
     * @return 计算后的线条宽度
     */
    fun getStrokeWidth(pressure: Float): Float {
        val normalizedPressure = pressure.coerceIn(0f, 1f)

        return when (this) {
            NORMAL -> {
                // 普通笔刷使用平滑的线性响应
                minWidth + (maxWidth - minWidth) * normalizedPressure
            }
            CALLIGRAPHY_PEN -> {
                // 秀丽笔使用非线性响应曲线，模拟真实笔刷特性
                getCalligraphyStrokeWidth(normalizedPressure)
            }
            INK_PENETRATION -> {
                // 墨水渗透笔使用物理模型的基础线宽计算
                getPhysicalPenetrationWidth(normalizedPressure)
            }
        }
    }
    
    /**
     * 秀丽笔专用的压感响应曲线
     * 基于真实秀丽笔的物理建模
     */
    private fun getCalligraphyStrokeWidth(pressure: Float): Float {
        // 使用物理建模计算真实的笔头变形
        val physics = CalligraphyPenPhysics()
        val brushState = physics.calculateBrushTipState(pressure, 0f)

        // 基于接触半径计算线条宽度
        val contactDiameter = brushState.contactRadius * 2f
        val physicalWidth = contactDiameter.coerceIn(minWidth, maxWidth)

        // 应用变形因子来调整最终宽度
        val deformationFactor = 1f + brushState.deformation * 0.5f

        return physicalWidth * deformationFactor
    }

    /**
     * 墨水渗透笔专用的固定基础线宽
     * 不受压感影响，让渗透效果成为唯一的线宽变化因素
     */
    private fun getPhysicalPenetrationWidth(pressure: Float): Float {
        // 返回固定的最小线宽，不受压感影响
        // 这样线宽变化完全由渗透效果决定
        return minWidth
    }
    
    /**
     * 获取墨水透明度
     * @param pressure 压感值
     * @return 透明度值 (0-255)
     */
    fun getInkAlpha(pressure: Float): Int {
        return when (this) {
            NORMAL -> 255 // 普通笔刷始终不透明
            CALLIGRAPHY_PEN -> {
                // 基于物理模型计算墨水透明度
                val physics = CalligraphyPenPhysics()
                val brushState = physics.calculateBrushTipState(pressure, 0f)
                val inkProperties = physics.calculateInkProperties(brushState, pressure)

                // 将物理透明度转换为Alpha值
                val alpha = ((1f - inkProperties.transparency) * 255f).toInt()
                alpha.coerceIn(100, 255)
            }
            INK_PENETRATION -> {
                // 墨水渗透笔使用基础透明度，实际透明度由InkPenetrationBrush处理
                255
            }
        }
    }
    
    /**
     * 获取笔刷颜色
     * @param baseColor 基础颜色
     * @param pressure 压感值
     * @return 调整后的颜色值
     */
    fun getBrushColor(baseColor: Int, pressure: Float): Int {
        return when (this) {
            NORMAL -> baseColor
            CALLIGRAPHY_PEN -> {
                // 基于物理模型计算墨水颜色
                val physics = CalligraphyPenPhysics()
                val brushState = physics.calculateBrushTipState(pressure, 0f)
                val inkProperties = physics.calculateInkProperties(brushState, pressure)

                // 根据墨水密度和饱和度计算颜色
                val density = inkProperties.density
                val saturation = inkProperties.saturation

                // 计算灰度值（密度越高越黑）
                val grayValue = ((1f - density) * 60f).toInt().coerceIn(0, 60)

                // 应用饱和度调整
                val finalGray = (grayValue * (1f - saturation * 0.5f)).toInt()

                android.graphics.Color.argb(
                    getInkAlpha(pressure),
                    finalGray, finalGray, finalGray
                )
            }
            INK_PENETRATION -> {
                // 墨水渗透笔使用基础黑色，实际颜色由InkPenetrationBrush处理
                android.graphics.Color.argb(
                    getInkAlpha(pressure),
                    0, 0, 0 // 纯黑色
                )
            }
        }
    }
    
    /**
     * 是否需要特殊的笔锋处理
     */
    fun needsSpecialTipHandling(): Boolean {
        return this == CALLIGRAPHY_PEN
    }

    /**
     * 是否需要墨水渗透处理
     */
    fun needsInkPenetration(): Boolean {
        return this == INK_PENETRATION
    }

    /**
     * 是否需要时间膨胀处理（向后兼容方法）
     */
    @Deprecated("使用 needsInkPenetration() 替代", ReplaceWith("needsInkPenetration()"))
    fun needsTimeDilation(): Boolean {
        return needsInkPenetration()
    }

    /**
     * 获取笔锋长度系数
     */
    fun getTipLengthMultiplier(): Float {
        return when (this) {
            NORMAL -> 1.0f
            CALLIGRAPHY_PEN -> 1.8f // 秀丽笔的笔锋更长
            INK_PENETRATION -> 0.8f   // 墨水渗透笔的笔锋较短
        }
    }
}
