package com.example.brushapp

import android.os.Build
import android.os.Bundle
import android.view.Display
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import com.example.brushapp.databinding.ActivityMainBinding

class MainActivity : AppCompatActivity() {

    private lateinit var binding: ActivityMainBinding
    private lateinit var handwritingSurfaceView: HandwritingSurfaceView
    private var deviceRefreshRate: Float = 60.0f
    private var currentStylusEventRate: Float = 0.0f
    private var currentFingerEventRate: Float = 0.0f

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main)) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }

        // 初始化手写SurfaceView
        handwritingSurfaceView = binding.handwritingSurfaceView

        // 设置状态变化监听器
        handwritingSurfaceView.onStateChangedListener = {
            updateButtonStates()
        }

        // 设置FPS更新监听器
        handwritingSurfaceView.setFpsUpdateListener { fps ->
            runOnUiThread {
                updatePerformanceInfo(fps)
            }
        }

        // 设置事件频率更新监听器
        handwritingSurfaceView.setEventRateUpdateListener { stylusRate, fingerRate ->
            runOnUiThread {
                currentStylusEventRate = stylusRate
                currentFingerEventRate = fingerRate
                updatePerformanceInfo(handwritingSurfaceView.getCurrentFps())
            }
        }

        // 设置按钮功能
        binding.buttonClear.setOnClickListener {
            handwritingSurfaceView.clearCanvas()
            updateButtonStates()
        }

        binding.buttonUndo.setOnClickListener {
            handwritingSurfaceView.undoLastStroke()
            updateButtonStates()
        }

        binding.buttonRedo.setOnClickListener {
            handwritingSurfaceView.redoLastStroke()
            updateButtonStates()
        }

        // 笔刷类型切换按钮
        binding.buttonBrushType.setOnClickListener {
            toggleBrushType()
        }

        // 初始化按钮状态
        updateButtonStates()

        // 显示设备刷新率
        displayRefreshRate()

        // 设置合理的压感敏感度（参考主流软件）
        handwritingSurfaceView.setPressureSensitivity(1.3f) // 1.3倍敏感度，更温和

        // 设置笔刷变化监听器
        handwritingSurfaceView.setBrushChangeListener { brushType ->
            updateBrushTypeButton(brushType)
        }

        // 初始化笔刷类型按钮状态
        updateBrushTypeButton(handwritingSurfaceView.getCurrentBrushType())
    }

    private fun updateButtonStates() {
        binding.buttonUndo.isEnabled = handwritingSurfaceView.canUndo()
        binding.buttonRedo.isEnabled = handwritingSurfaceView.canRedo()
    }

    private fun displayRefreshRate() {
        try {
            val display = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                // Android 11+ (API 30+)
                display
            } else {
                // Android 10 及以下版本
                @Suppress("DEPRECATION")
                windowManager.defaultDisplay
            }

            val refreshRate = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                // Android 6.0+ (API 23+) 支持多种刷新率
                val currentMode = display?.mode
                currentMode?.refreshRate ?: 60.0f
            } else {
                // 旧版本Android
                @Suppress("DEPRECATION")
                display?.refreshRate ?: 60.0f
            }

            deviceRefreshRate = refreshRate
            val eventRates = handwritingSurfaceView.getCurrentEventRates()
            currentStylusEventRate = eventRates.first
            currentFingerEventRate = eventRates.second
            updatePerformanceInfo(handwritingSurfaceView.getCurrentFps())

        } catch (e: Exception) {
            // 如果获取刷新率失败，使用默认值
            deviceRefreshRate = 60.0f
            val eventRates = handwritingSurfaceView.getCurrentEventRates()
            currentStylusEventRate = eventRates.first
            currentFingerEventRate = eventRates.second
            updatePerformanceInfo(handwritingSurfaceView.getCurrentFps())
        }
    }

    private fun updatePerformanceInfo(currentFps: Float) {
        val performanceStats = handwritingSurfaceView.getPerformanceStats()
        val eventStats = handwritingSurfaceView.getEventStats()
        // 添加墨水渗透笔刷调试信息
        val penetrationInfo = if (handwritingSurfaceView.getCurrentBrushType() == BrushType.INK_PENETRATION) {
            "\n渗透笔: " + handwritingSurfaceView.getPenetrationInfo()
        } else {
            ""
        }

        val performanceText = getString(
            R.string.performance_with_events,
            deviceRefreshRate,
            currentFps,
            currentStylusEventRate,
            currentFingerEventRate,
            "$performanceStats\n$eventStats"
        ) + penetrationInfo
        binding.textViewRefreshRate.text = performanceText
    }

    private fun toggleBrushType() {
        val currentBrushType = handwritingSurfaceView.getCurrentBrushType()
        val newBrushType = when (currentBrushType) {
            BrushType.NORMAL -> BrushType.CALLIGRAPHY_PEN
            BrushType.CALLIGRAPHY_PEN -> BrushType.INK_PENETRATION
            BrushType.INK_PENETRATION -> BrushType.NORMAL
        }
        handwritingSurfaceView.setBrushType(newBrushType)
    }

    private fun updateBrushTypeButton(brushType: BrushType) {
        when (brushType) {
            BrushType.NORMAL -> {
                binding.buttonBrushType.text = getString(R.string.button_normal_brush)
                binding.buttonBrushType.backgroundTintList =
                    androidx.core.content.ContextCompat.getColorStateList(this, R.color.teal_200)
            }
            BrushType.CALLIGRAPHY_PEN -> {
                binding.buttonBrushType.text = getString(R.string.button_calligraphy_brush)
                binding.buttonBrushType.backgroundTintList =
                    androidx.core.content.ContextCompat.getColorStateList(this, R.color.purple_200)
            }
            BrushType.INK_PENETRATION -> {
                binding.buttonBrushType.text = getString(R.string.button_ink_penetration_brush)
                binding.buttonBrushType.backgroundTintList =
                    androidx.core.content.ContextCompat.getColorStateList(this, R.color.teal_700)
            }
        }
    }
}
