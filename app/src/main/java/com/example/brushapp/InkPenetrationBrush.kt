package com.example.brushapp

import kotlin.math.*

/**
 * 墨水渗透笔刷 - 基于真实物理模型的墨水渗透模拟
 *
 * 核心特性：
 * 1. 基于Fick扩散定律的真实墨水扩散
 * 2. <PERSON>-<PERSON><PERSON><PERSON>毛细作用模拟
 * 3. 达西定律的多孔介质流动
 * 4. 多点渗透的叠加效应
 * 5. 自然的浓度分布和边界
 *
 * 物理原理：
 * - Fick扩散定律：J = -D∇C（浓度梯度驱动扩散）
 * - <PERSON>-<PERSON><PERSON><PERSON>方程：L² = (γcosθrt)/(2η)（毛细渗透）
 * - 达西定律：v = -(k/μ)∇P（多孔介质流动）
 */
class InkPenetrationBrush {

    companion object {
        // 物理模型参数 - 强化对比效果
        const val INITIAL_WIDTH = 0.8f        // 初始线宽 (更细的起始线宽)
        const val MAX_WIDTH_MULTIPLIER = 20f   // 最大线宽倍数 (增加到20倍，更明显对比)
        const val PENETRATION_THRESHOLD = 0.1f // 开始渗透的最小停留时间 (100毫秒，更容易触发)
        const val PENETRATION_INTENSITY = 4.0f // 渗透强度系数 (大幅增强效果)
        const val MAX_PENETRATION_POINTS = 8   // 最大同时渗透点数量
        const val DWELL_SENSITIVITY = 6.0f     // 停留敏感度 (更快响应)
    }
    
    // 核心状态
    private var currentDwellTime = 0f          // 当前停留时间
    private var lastPosition = Pair(0f, 0f)   // 上次位置
    private var lastUpdateTime = 0L           // 上次更新时间
    private var isActive = false              // 是否正在绘制
    private var currentWidth = INITIAL_WIDTH  // 当前线宽

    // 墨水渗透引擎
    private val inkPenetrationEngine = InkPenetrationEngine()
    private var currentPenetrationPoint: InkPenetrationEngine.PenetrationPoint? = null
    private var penetrationStartTime = 0L     // 渗透开始时间
    private var dwellStartTime = 0L           // 停留开始时间
    private var stopTime = 0L                 // 停止绘制的时间
    private var hasLoggedTimeout = false       // 是否已经记录过超时日志
    
    /**
     * 开始绘制 - 完全基于物理模型
     */
    fun startDrawing(x: Float, y: Float, currentTime: Long) {
        isActive = true
        currentDwellTime = 0f
        lastPosition = Pair(x, y)
        lastUpdateTime = currentTime
        currentWidth = INITIAL_WIDTH
        penetrationStartTime = currentTime
        dwellStartTime = currentTime
        stopTime = 0L
        hasLoggedTimeout = false

        // 不清除渗透引擎，保持累积效果
        // inkPenetrationEngine.clearAll() // 注释掉，保持渗透点累积
        currentPenetrationPoint = null

        // 临时添加更强的立即测试渗透点，验证渗透效果
        val testPoint = inkPenetrationEngine.startPenetration(x, y, 10.0f) // 增加到10.0f
        android.util.Log.d("InkPenetration", "开始绘制: 位置($x, $y), 初始线宽=$INITIAL_WIDTH")
        android.util.Log.d("InkPenetration", "立即测试渗透点: 浓度=${testPoint.concentration}, 半径=${testPoint.radius}")

        // 立即测试这个位置的浓度和倍数
        val immediateConcentration = inkPenetrationEngine.calculateConcentrationAt(x, y)
        val immediateMultiplier = inkPenetrationEngine.calculateWidthMultiplier(x, y, INITIAL_WIDTH)
        android.util.Log.d("InkPenetration",
            "🔬 立即验证: 位置($x, $y) → 浓度=$immediateConcentration, 倍数=$immediateMultiplier")
    }
    
    /**
     * 更新绘制状态 - 完全基于墨水渗透物理模型
     */
    fun updateDrawing(x: Float, y: Float, currentTime: Long): Float {
        if (!isActive) return INITIAL_WIDTH

        val deltaTime = if (lastUpdateTime > 0) {
            (currentTime - lastUpdateTime) / 1_000_000_000f
        } else {
            0f
        }

        // 计算移动距离
        val moveDistance = sqrt((x - lastPosition.first).pow(2) + (y - lastPosition.second).pow(2))
        val moveThreshold = 8f // 放宽停留检测，允许轻微手抖

        if (moveDistance < moveThreshold) {
            // 停留状态：累积时间，强化墨水渗透
            currentDwellTime += deltaTime

            // 调试日志
            android.util.Log.d("InkPenetration", "停留检测: 移动距离=$moveDistance, 停留时间=$currentDwellTime")

            // 动态调整渗透强度
            val dwellIntensity = (currentDwellTime * DWELL_SENSITIVITY).coerceAtMost(3f)

            // 检查是否需要开始新的渗透点或增强现有渗透
            if (currentDwellTime >= PENETRATION_THRESHOLD) {
                if (currentPenetrationPoint == null) {
                    // 开始新的渗透点
                    val intensity = PENETRATION_INTENSITY * dwellIntensity
                    currentPenetrationPoint = inkPenetrationEngine.startPenetration(x, y, intensity)
                    android.util.Log.d("InkPenetration", "创建渗透点: 位置($x, $y), 强度=$intensity, 浓度=${currentPenetrationPoint!!.concentration}")
                } else {
                    // 增强现有渗透点（如果位置接近）
                    val distance = sqrt((x - currentPenetrationPoint!!.x).pow(2) + (y - currentPenetrationPoint!!.y).pow(2))
                    if (distance < 15f) {
                        // 增强现有点的浓度，更明显的增强效果
                        currentPenetrationPoint!!.concentration =
                            (currentPenetrationPoint!!.concentration + dwellIntensity * 0.3f).coerceAtMost(4f)
                    } else {
                        // 位置变化较大，创建新的渗透点
                        if (inkPenetrationEngine.getActivePenetrationCount() < MAX_PENETRATION_POINTS) {
                            val intensity = PENETRATION_INTENSITY * dwellIntensity
                            currentPenetrationPoint = inkPenetrationEngine.startPenetration(x, y, intensity)
                        }
                    }
                }
            }
        } else {
            // 移动状态：重置停留时间，但保持渗透点活跃
            currentDwellTime = 0f
            lastPosition = Pair(x, y)
            dwellStartTime = currentTime
            currentPenetrationPoint = null // 结束当前渗透点的直接控制

            // 在移动路径上创建更强的渗透点，确保整条线都有渗透效果
            if (inkPenetrationEngine.getActivePenetrationCount() < MAX_PENETRATION_POINTS) {
                val moveIntensity = PENETRATION_INTENSITY * 2.0f // 使用双倍强度
                val movePoint = inkPenetrationEngine.startPenetration(x, y, moveIntensity)
                android.util.Log.d("InkPenetration",
                    "移动渗透点: 位置($x, $y), 强度=$moveIntensity, 浓度=${movePoint.concentration}")

                // 立即测试移动位置的浓度
                val moveConcentration = inkPenetrationEngine.calculateConcentrationAt(x, y)
                android.util.Log.d("InkPenetration",
                    "🔬 移动验证: 位置($x, $y) → 浓度=$moveConcentration")
            }
        }

        // 持续更新墨水渗透引擎
        inkPenetrationEngine.updatePenetration(currentTime)

        // 完全基于物理模型计算线宽
        val penetrationMultiplier = inkPenetrationEngine.calculateWidthMultiplier(x, y, INITIAL_WIDTH)
        val physicalWidth = INITIAL_WIDTH * penetrationMultiplier.coerceAtMost(MAX_WIDTH_MULTIPLIER)

        // 应用平滑过渡，更快的响应速度
        val smoothingFactor = 0.6f // 降低平滑因子，让变化更快
        currentWidth = currentWidth * smoothingFactor + physicalWidth * (1f - smoothingFactor)

        lastUpdateTime = currentTime
        return currentWidth
    }
    
    /**
     * 停止绘制 - 让渗透效果自然衰减
     */
    fun stopDrawing() {
        isActive = false
        currentPenetrationPoint = null
        currentDwellTime = 0f
        stopTime = System.nanoTime()
        // 保持渗透引擎运行，让效果自然衰减
        android.util.Log.d("InkPenetration", "停止绘制，开始自然衰减")
    }
    
    /**
     * 获取当前线宽
     */
    fun getCurrentWidth(): Float {
        return currentWidth
    }

    /**
     * 获取当前停留时间
     */
    fun getCurrentDwellTime(): Float {
        return currentDwellTime
    }

    /**
     * 获取基础线宽（用于压感计算）
     */
    fun getBaseWidth(pressure: Float): Float {
        val pressureMultiplier = (0.5f + pressure * 0.5f).coerceIn(0.8f, 1.2f)
        return INITIAL_WIDTH * pressureMultiplier
    }
    
    /**
     * 重置状态
     */
    fun reset() {
        isActive = false
        currentDwellTime = 0f
        currentWidth = INITIAL_WIDTH
        lastUpdateTime = 0L
    }
    


    /**
     * 计算停留位置对指定点的影响 - 完全基于墨水渗透模型
     * @param dwellX 停留位置X (保留用于兼容性，实际使用渗透引擎)
     * @param dwellY 停留位置Y (保留用于兼容性，实际使用渗透引擎)
     * @param dwellTime 停留时间 (保留用于兼容性，实际使用渗透引擎)
     * @param pointX 目标点X
     * @param pointY 目标点Y
     * @return 影响强度 (0.0 - 1.0)
     */
    fun calculateInfluence(dwellX: Float, dwellY: Float, dwellTime: Float, pointX: Float, pointY: Float): Float {
        // 完全基于物理模型的浓度计算
        return inkPenetrationEngine.calculateConcentrationAt(pointX, pointY)
    }

    /**
     * 根据影响强度计算额外的线宽增量 - 基于物理模型
     */
    fun calculateWidthIncrease(influence: Float): Float {
        return influence * (INITIAL_WIDTH * MAX_WIDTH_MULTIPLIER - INITIAL_WIDTH)
    }

    /**
     * 计算指定位置的线宽倍数 - 完全基于墨水渗透模型
     */
    fun calculateWidthMultiplierAt(x: Float, y: Float): Float {
        val multiplier = inkPenetrationEngine.calculateWidthMultiplier(x, y, INITIAL_WIDTH)

        // 详细的倍数计算日志
        android.util.Log.d("InkPenetration",
            "🔢 倍数计算: 位置(${String.format("%.1f", x)}, ${String.format("%.1f", y)}) → " +
            "倍数=${String.format("%.3f", multiplier)}, " +
            "活跃渗透点=${inkPenetrationEngine.getActivePenetrationCount()}")

        return multiplier
    }

    /**
     * 获取墨水渗透的详细信息
     */
    fun getPenetrationInfo(): String {
        val penetrationInfo = inkPenetrationEngine.getDebugInfo()
        val dwellInfo = "停留: %.3fs, 线宽: %.1fpx".format(currentDwellTime, currentWidth)
        val activePoints = inkPenetrationEngine.getActivePenetrationCount()
        val intensity = getCurrentPenetrationIntensity()
        val multiplier = if (lastPosition.first != 0f || lastPosition.second != 0f) {
            inkPenetrationEngine.calculateWidthMultiplier(lastPosition.first, lastPosition.second, INITIAL_WIDTH)
        } else 1f
        return "$dwellInfo | 渗透点: $activePoints | 强度: %.2f | 倍数: %.1fx | $penetrationInfo".format(intensity, multiplier)
    }

    /**
     * 强制更新墨水渗透状态 - 用于连续渲染
     */
    fun updatePenetrationState(currentTime: Long) {
        inkPenetrationEngine.updatePenetration(currentTime)
    }

    /**
     * 检查是否有活跃的渗透效果
     * 添加时间限制，避免无限持续更新
     */
    fun hasActivePenetration(): Boolean {
        val hasActivePoints = inkPenetrationEngine.getActivePenetrationCount() > 0

        // 如果已经停止绘制超过3秒，强制停止更新
        if (stopTime > 0 && System.nanoTime() - stopTime > 3_000_000_000L) {
            if (!hasLoggedTimeout) {
                android.util.Log.d("InkPenetration", "渗透效果超时，强制停止更新")
                hasLoggedTimeout = true
                // 清理所有渗透状态，彻底停止
                inkPenetrationEngine.clearAll()
            }
            return false
        }

        return hasActivePoints
    }

    /**
     * 清理所有渗透状态
     */
    fun clearPenetrationState() {
        inkPenetrationEngine.clearAll()
        currentPenetrationPoint = null
        currentDwellTime = 0f
    }

    /**
     * 强制创建测试渗透点 - 用于调试
     */
    fun createTestPenetration(x: Float, y: Float) {
        val testPoint = inkPenetrationEngine.startPenetration(x, y, 3.0f)
        android.util.Log.d("InkPenetration", "创建测试渗透点: 位置($x, $y), 浓度=${testPoint.concentration}")
    }

    /**
     * 获取渗透引擎的直接访问（用于高级用法）
     */
    fun getPenetrationEngine(): InkPenetrationEngine {
        return inkPenetrationEngine
    }

    /**
     * 获取当前渗透强度（0-1范围）
     */
    fun getCurrentPenetrationIntensity(): Float {
        val maxConcentration = 2f // 基于InkPenetrationEngine的最大浓度
        var maxCurrentConcentration = 0f

        // 在当前位置周围采样找到最大浓度
        val sampleRadius = 10f
        for (dx in -sampleRadius.toInt()..sampleRadius.toInt() step 2) {
            for (dy in -sampleRadius.toInt()..sampleRadius.toInt() step 2) {
                val x = lastPosition.first + dx
                val y = lastPosition.second + dy
                val concentration = inkPenetrationEngine.calculateConcentrationAt(x, y)
                maxCurrentConcentration = maxOf(maxCurrentConcentration, concentration)
            }
        }

        return (maxCurrentConcentration / maxConcentration).coerceIn(0f, 1f)
    }
}
