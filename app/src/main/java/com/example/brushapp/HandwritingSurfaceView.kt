package com.example.brushapp

import android.content.Context
import android.graphics.*
import android.os.Build
import android.util.AttributeSet
import android.view.Display
import android.view.MotionEvent
import android.view.SurfaceHolder
import android.view.SurfaceView
import android.view.WindowManager
import kotlin.math.abs
import kotlin.math.sqrt
import kotlin.math.pow
import kotlin.math.exp

class HandwritingSurfaceView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : SurfaceView(context, attrs, defStyleAttr), SurfaceHolder.Callback {

    // 移除绘制线程，改为UI线程绘制
    private val uiHandler = android.os.Handler(android.os.Looper.getMainLooper())
    private var penetrationUpdateRunnable: Runnable? = null
    private val paths = mutableListOf<StrokePath>()
    private var currentPath: StrokePath? = null

    // 重做功能：存储被撤销的笔画
    private val redoStack = mutableListOf<StrokePath>()
    
    // 绘制相关
    private val paint = Paint().apply {
        isAntiAlias = true
        isDither = true
        style = Paint.Style.STROKE
        strokeJoin = Paint.Join.ROUND
        strokeCap = Paint.Cap.ROUND
        color = Color.BLACK
    }
    
    // 触摸相关
    private var lastX = 0f
    private var lastY = 0f
    private var lastPressure = 0f
    private val touchTolerance = 4f
    
    // 背景颜色
    private val backgroundColor = Color.WHITE

    // 状态变化回调
    var onStateChangedListener: (() -> Unit)? = null

    // 设备刷新率
    private var displayRefreshRate: Float = 60.0f

    // 绘制状态控制
    private var needsRedraw = true
    private var isDrawing = false // 是否正在绘制中
    private var continuousDrawing = false // 是否需要连续绘制

    // FPS计算相关 - 简化版本
    private var currentFps = 0.0f
    private var fpsUpdateListener: ((Float) -> Unit)? = null

    // 性能调试信息 - 简化版本
    private var totalFrames = 0L
    private var droppedFrames = 0L

    // 手写笔事件统计
    private var stylusEventCount = 0
    private var fingerEventCount = 0
    private var lastEventStatsTime = 0L
    private var currentStylusEventRate = 0.0f
    private var currentFingerEventRate = 0.0f
    private var eventRateUpdateListener: ((Float, Float) -> Unit)? = null

    // 详细事件类型统计
    private var stylusDownCount = 0
    private var stylusMoveCount = 0
    private var stylusUpCount = 0
    private var fingerDownCount = 0
    private var fingerMoveCount = 0
    private var fingerUpCount = 0

    // 压感平滑相关
    private val pressureHistory = mutableListOf<Float>()
    private val maxPressureHistorySize = 5 // 保留最近5个压感值
    private var smoothedPressure = 0.5f

    // 压感敏感度控制（参考主流软件的合理范围）
    private var pressureSensitivity = 1.2f // 压感放大系数，默认1.2倍（更温和）

    // 笔刷类型管理
    private var currentBrushType = BrushType.NORMAL
    private var brushChangeListener: ((BrushType) -> Unit)? = null

    // 物理建模相关
    private val calligraphyPhysics = CalligraphyPenPhysics()
    private var previousBrushState: CalligraphyPenPhysics.BrushTipState? = null
    private var lastPhysicsUpdateTime = 0L

    // 墨水渗透笔刷相关
    private val inkPenetrationBrush = InkPenetrationBrush()
    private var isPenetrationBrushActive = false

    // 笔锋效果相关（参考主流软件）
    private var isLiftingPen = false // 是否正在抬笔
    private var liftStartTime = 0L // 抬笔开始时间
    private val liftDuration = 150_000_000L // 抬笔渐变持续时间（150ms）
    private var originalLiftPressure = 0.5f // 抬笔时的原始压感

    // 速度和方向追踪（OneNote风格）
    private val velocityHistory = mutableListOf<Float>()
    private val directionHistory = mutableListOf<Pair<Float, Float>>()
    private val maxHistorySize = 8
    private var lastMoveTime = 0L

    init {
        holder.addCallback(this)
        isFocusable = true
        getDisplayRefreshRate()
    }

    override fun surfaceCreated(holder: SurfaceHolder) {
        // 移除绘制线程，改为UI线程绘制
        // 初始绘制
        drawOnCanvas()
    }

    /**
     * 在UI线程上绘制到Canvas
     */
    private fun drawOnCanvas() {
        val canvas = holder.lockCanvas()
        try {
            canvas?.let {
                // 清除画布
                it.drawColor(backgroundColor)

                // 绘制所有已完成的路径
                for (strokePath in paths) {
                    drawStrokePath(it, strokePath)
                }

                // 绘制当前正在绘制的路径
                currentPath?.let { path ->
                    drawStrokePath(it, path)
                }

                // 更新渗透笔状态（如果需要）
                if (currentBrushType.needsInkPenetration() && isPenetrationBrushActive && isDrawing) {
                    val currentTime = System.nanoTime()
                    inkPenetrationBrush.updateDrawing(lastX, lastY, currentTime)

                    // 更新当前路径的最后一个点的线宽
                    currentPath?.let { path ->
                        if (path.points.isNotEmpty()) {
                            val lastPoint = path.points.last()
                            lastPoint.dwellTime = inkPenetrationBrush.getCurrentDwellTime()
                            lastPoint.finalWidth = inkPenetrationBrush.getCurrentWidth()
                        }
                    }

                    // 安排下一次更新
                    scheduleNextPenetrationUpdate()
                }

                // 更新性能统计
                totalFrames++
            }
        } catch (e: Exception) {
            e.printStackTrace()
            droppedFrames++
        } finally {
            canvas?.let {
                holder.unlockCanvasAndPost(it)
            }
        }
    }

    /**
     * 安排下一次渗透笔更新
     */
    private fun scheduleNextPenetrationUpdate() {
        // 取消之前的更新
        penetrationUpdateRunnable?.let { uiHandler.removeCallbacks(it) }

        // 只在渗透笔活跃且正在绘制时安排更新
        if (currentBrushType.needsInkPenetration() && isPenetrationBrushActive && isDrawing) {
            penetrationUpdateRunnable = Runnable {
                drawOnCanvas()
            }
            // 16ms ≈ 60fps
            uiHandler.postDelayed(penetrationUpdateRunnable!!, 16)
        }
    }

    /**
     * 停止渗透笔更新
     */
    private fun stopPenetrationUpdate() {
        penetrationUpdateRunnable?.let { uiHandler.removeCallbacks(it) }
        penetrationUpdateRunnable = null
    }

    private fun drawStrokePath(canvas: Canvas, strokePath: StrokePath) {
        // 移除同步代码，因为现在在UI线程上运行
        val points = strokePath.points
        if (points.size < 2) return

        // 直接绘制主笔画
        drawSmoothStroke(canvas, points)
    }

    private fun drawSmoothStroke(canvas: Canvas, points: List<StrokePoint>) {
        if (points.size < 2) return

        // 获取笔刷类型（从第一个点获取）
        val brushType = points.firstOrNull()?.brushType ?: BrushType.NORMAL

        for (i in 0 until points.size - 1) {
            val startPoint = points[i]
            val endPoint = points[i + 1]

            // 根据笔刷类型计算宽度
            var startWidth = brushType.getStrokeWidth(startPoint.pressure)
            var endWidth = brushType.getStrokeWidth(endPoint.pressure)

            // 对于墨水渗透笔，需要应用渗透效果到起始和结束宽度
            if (brushType == BrushType.INK_PENETRATION) {
                val originalStartWidth = startWidth
                val originalEndWidth = endWidth

                // 使用真实的渗透计算
                val startMultiplier = inkPenetrationBrush.calculateWidthMultiplierAt(startPoint.x, startPoint.y)
                val endMultiplier = inkPenetrationBrush.calculateWidthMultiplierAt(endPoint.x, endPoint.y)

                // 应用倍数
                startWidth = startWidth * startMultiplier
                endWidth = endWidth * endMultiplier

                android.util.Log.d("InkPenetration",
                    "🔢 真实倍数应用: start=${String.format("%.3f", startMultiplier)}x (${String.format("%.2f", startWidth)}px), " +
                    "end=${String.format("%.3f", endMultiplier)}x (${String.format("%.2f", endWidth)}px)")

                // 详细的线段宽度日志 - 降低阈值，显示更多信息
                if (isPenetrationBrushActive) {
                    android.util.Log.d("InkPenetration",
                        "📏 线段宽度计算: " +
                        "起点(${String.format("%.1f", startPoint.x)}, ${String.format("%.1f", startPoint.y)}) " +
                        "原始=${String.format("%.2f", originalStartWidth)}px → " +
                        "渗透=${String.format("%.2f", startWidth)}px (${String.format("%.2f", startMultiplier)}x), " +
                        "终点(${String.format("%.1f", endPoint.x)}, ${String.format("%.1f", endPoint.y)}) " +
                        "原始=${String.format("%.2f", originalEndWidth)}px → " +
                        "渗透=${String.format("%.2f", endWidth)}px (${String.format("%.2f", endMultiplier)}x)")
                }
            }

            // 绘制渐变宽度的线段
            drawVariableWidthLine(canvas, startPoint, endPoint, startWidth, endWidth, brushType)
        }
    }

    private fun drawVariableWidthLine(
        canvas: Canvas,
        startPoint: StrokePoint,
        endPoint: StrokePoint,
        startWidth: Float,
        endWidth: Float,
        brushType: BrushType = BrushType.NORMAL
    ) {
        // 根据笔刷类型调整段数
        val segments = when (brushType) {
            BrushType.NORMAL -> {
                val isLowPressure = (startPoint.pressure < 0.3f || endPoint.pressure < 0.3f)
                if (isLowPressure) 8 else 5
            }
            BrushType.CALLIGRAPHY_PEN -> {
                // 秀丽笔需要更多段数来实现平滑的粗细变化
                12
            }
            BrushType.INK_PENETRATION -> {
                // 墨水渗透笔需要更多段数来实现平滑的渗透效果
                10
            }
        }

        for (i in 0 until segments) {
            val t1 = i.toFloat() / segments
            val t2 = (i + 1).toFloat() / segments

            val x1 = startPoint.x + (endPoint.x - startPoint.x) * t1
            val y1 = startPoint.y + (endPoint.y - startPoint.y) * t1
            val x2 = startPoint.x + (endPoint.x - startPoint.x) * t2
            val y2 = startPoint.y + (endPoint.y - startPoint.y) * t2

            val t = (t1 + t2) / 2f
            val interpolatedPressure = startPoint.pressure + (endPoint.pressure - startPoint.pressure) * t
            var width = startWidth + (endWidth - startWidth) * t

            // 设置画笔属性
            paint.strokeWidth = width
            paint.strokeCap = Paint.Cap.ROUND
            paint.style = Paint.Style.STROKE

            // 详细的画笔设置日志
            if (brushType == BrushType.INK_PENETRATION) {
                val segmentIndex = i + 1
                val totalSegments = segments
                android.util.Log.d("InkPenetration",
                    "🎨 画笔设置 [段落 $segmentIndex/$totalSegments]: " +
                    "插值t=${String.format("%.3f", t)}, " +
                    "计算宽度=${String.format("%.2f", width)}px, " +
                    "实际strokeWidth=${String.format("%.2f", paint.strokeWidth)}px, " +
                    "起始=${String.format("%.2f", startWidth)}px, " +
                    "结束=${String.format("%.2f", endWidth)}px")
            }

            // 根据笔刷类型设置颜色和效果
            when (brushType) {
                BrushType.NORMAL -> {
                    paint.alpha = 255
                    paint.color = Color.BLACK
                    paint.maskFilter = null
                }
                BrushType.CALLIGRAPHY_PEN -> {
                    // 秀丽笔的高级视觉效果
                    val brushState = startPoint.brushState
                    val inkProps = startPoint.inkProperties

                    if (brushState != null && inkProps != null) {
                        // 基于物理状态的高级渲染
                        val density = inkProps.density
                        val inkFlow = brushState.inkFlow

                        // 更自然的透明度计算
                        val baseAlpha = 200
                        val densityAlpha = (density * 55f).toInt()
                        val flowAlpha = (inkFlow * 25f).toInt()
                        val alpha = (baseAlpha + densityAlpha + flowAlpha).coerceIn(180, 255)

                        // 更丰富的颜色变化
                        val grayValue = ((1f - density) * 60f).toInt().coerceIn(0, 60)

                        paint.alpha = alpha
                        paint.color = android.graphics.Color.argb(alpha, grayValue, grayValue, grayValue)

                        // 基于扩散半径添加边缘效果
                        val diffusionRadius = inkProps.diffusionRadius
                        if (diffusionRadius > width * 0.1f) {
                            paint.maskFilter = android.graphics.BlurMaskFilter(
                                (diffusionRadius * 0.1f).coerceIn(0.2f, 1.5f),
                                android.graphics.BlurMaskFilter.Blur.NORMAL
                            )
                        } else {
                            paint.maskFilter = null
                        }
                    } else {
                        // 回退到基本实现
                        paint.alpha = brushType.getInkAlpha(interpolatedPressure)
                        paint.color = brushType.getBrushColor(Color.BLACK, interpolatedPressure)
                        paint.maskFilter = null
                    }
                }
                BrushType.INK_PENETRATION -> {
                    // 墨水渗透笔刷已经在调用前应用了渗透效果到startWidth和endWidth
                    // 这里只需要正常的线性插值

                    // 使用已经应用了渗透效果的startWidth和endWidth进行插值
                    // width已经通过startWidth + (endWidth - startWidth) * t计算

                    // 调试日志 - 只在活跃绘制且线宽较大时输出
                    if (isPenetrationBrushActive && width > 1.0f) {
                        val currentX = x1 + (x2 - x1) * t
                        val currentY = y1 + (y2 - y1) * t
                        android.util.Log.d("InkPenetration", "渲染段落: 位置($currentX, $currentY), 最终宽度=$width")
                    }

                    // 墨水渗透笔刷的高级视觉效果
                    val currentX = x1 + (x2 - x1) * t
                    val currentY = y1 + (y2 - y1) * t

                    // 获取当前位置的物理墨水浓度
                    val concentration = inkPenetrationBrush.getPenetrationEngine()
                        .calculateConcentrationAt(currentX, currentY)

                    // 基于物理浓度的高级视觉效果
                    val baseAlpha = 180 // 降低基础透明度，让渗透效果更明显
                    val concentrationAlpha = (concentration * 120f).toInt() // 增强浓度影响
                    val finalAlpha = (baseAlpha + concentrationAlpha).coerceIn(140, 255)

                    // 更真实的墨水颜色变化
                    val baseGray = 45 // 基础灰度值
                    val concentrationGray = (concentration * 40f).toInt() // 更强的浓度加深
                    val finalGray = (baseGray - concentrationGray).coerceIn(0, 70)

                    paint.alpha = finalAlpha
                    paint.color = android.graphics.Color.argb(finalAlpha, finalGray, finalGray, finalGray)

                    // 基于物理扩散的边缘效果
                    if (concentration > 0.2f) {
                        // 更敏感的模糊触发，更自然的模糊半径
                        val blurRadius = (concentration * 3f + 0.5f).coerceIn(0.8f, 4f)
                        paint.maskFilter = android.graphics.BlurMaskFilter(
                            blurRadius,
                            android.graphics.BlurMaskFilter.Blur.NORMAL
                        )
                    } else {
                        paint.maskFilter = null
                    }

                    // 添加轻微的颜色变化以模拟墨水密度差异
                    if (concentration > 0.5f) {
                        val densityVariation = (concentration * 15f).toInt()
                        val redShift = (finalGray - densityVariation).coerceAtLeast(0)
                        val blueShift = (finalGray + densityVariation / 2).coerceAtMost(255)
                        paint.color = android.graphics.Color.argb(finalAlpha, redShift, finalGray, blueShift)
                    }
                }
            }

            // 最终绘制日志
            if (brushType == BrushType.INK_PENETRATION) {
                android.util.Log.d("InkPenetration",
                    "✏️ 绘制线段: 从(${String.format("%.1f", x1)}, ${String.format("%.1f", y1)}) " +
                    "到(${String.format("%.1f", x2)}, ${String.format("%.1f", y2)}), " +
                    "线宽=${String.format("%.2f", paint.strokeWidth)}px, " +
                    "颜色=0x${Integer.toHexString(paint.color)}, " +
                    "透明度=${paint.alpha}")
            }

            canvas.drawLine(x1, y1, x2, y2, paint)
        }
    }

    override fun surfaceChanged(holder: SurfaceHolder, format: Int, width: Int, height: Int) {
        // Surface尺寸改变时的处理
    }

    override fun surfaceDestroyed(holder: SurfaceHolder) {
        // 移除绘制线程相关清理代码
        // 停止渗透笔更新
        stopPenetrationUpdate()

        // 清理渗透笔状态
        if (currentBrushType.needsInkPenetration()) {
            inkPenetrationBrush.clearPenetrationState()
        }
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        val x = event.x
        val y = event.y
        val pressure = event.pressure

        // 检测手写笔输入
        val isStylus = event.getToolType(0) == MotionEvent.TOOL_TYPE_STYLUS

        val actionType = event.action and MotionEvent.ACTION_MASK

        // 统计事件频率（包括事件类型）
        recordInputEvent(isStylus, actionType)

        when (actionType) {
            MotionEvent.ACTION_DOWN -> {
                touchStart(x, y, pressure, isStylus)
                isDrawing = true
                continuousDrawing = true
                needsRedraw = true

                // 墨水渗透笔刷处理
                if (currentBrushType.needsInkPenetration()) {
                    inkPenetrationBrush.startDrawing(x, y, System.nanoTime())
                    isPenetrationBrushActive = true
                    android.util.Log.d("InkPenetration",
                        "🖊️ 开始绘制: 位置(${String.format("%.1f", x)}, ${String.format("%.1f", y)}), " +
                        "压感=${String.format("%.3f", smoothedPressure)}, " +
                        "基础线宽=${currentBrushType.minWidth}px")
                }
            }
            MotionEvent.ACTION_MOVE -> {
                touchMove(x, y, pressure, isStylus)
                needsRedraw = true

                // 墨水渗透笔刷处理 - 移动时更新状态
                if (currentBrushType.needsInkPenetration() && isPenetrationBrushActive) {
                    inkPenetrationBrush.updateDrawing(x, y, System.nanoTime())
                }
            }
            MotionEvent.ACTION_UP -> {
                startPenLift(x, y, pressure, isStylus)
                isDrawing = false
                // 继续绘制几帧以确保笔锋效果完全显示
                continuousDrawing = true
                needsRedraw = true

                // 墨水渗透笔刷处理
                if (currentBrushType.needsInkPenetration()) {
                    inkPenetrationBrush.stopDrawing()
                    isPenetrationBrushActive = false
                }
            }
            MotionEvent.ACTION_CANCEL -> {
                touchUp()
                isDrawing = false
                continuousDrawing = false
                needsRedraw = true

                // 墨水渗透笔刷处理
                if (currentBrushType.needsInkPenetration()) {
                    inkPenetrationBrush.stopDrawing()
                    isPenetrationBrushActive = false
                }
            }
        }
        return true
    }

    private fun touchStart(x: Float, y: Float, pressure: Float, isStylus: Boolean = false) {
        // 开始新笔画时清除重做栈
        redoStack.clear()

        // 重置压感历史
        pressureHistory.clear()
        smoothedPressure = pressure
        pressureHistory.add(pressure)

        currentPath = StrokePath().apply {
            brushType = currentBrushType
            moveTo(x, y)

            // 墨水渗透笔刷需要传递停留时间和线宽信息
            val dwellTime = if (currentBrushType.needsInkPenetration()) inkPenetrationBrush.getCurrentDwellTime() else 0f
            val finalWidth = if (currentBrushType.needsInkPenetration()) inkPenetrationBrush.getCurrentWidth() else 0f

            addPoint(x, y, smoothedPressure, isStylus, currentBrushType, 0f, null, null, dwellTime, finalWidth)
        }
        lastX = x
        lastY = y
        lastPressure = smoothedPressure

        // 立即绘制到UI线程
        drawOnCanvas()
    }

    private fun touchMove(x: Float, y: Float, pressure: Float, isStylus: Boolean = false) {
        val dx = abs(x - lastX)
        val dy = abs(y - lastY)

        if (dx >= touchTolerance || dy >= touchTolerance) {
            // 计算速度和方向（主流软件风格）
            val currentTime = System.nanoTime()
            updateVelocityAndDirection(x, y, currentTime)

            // 平滑压感数据
            smoothedPressure = smoothPressure(pressure)

            // 计算当前速度
            val currentVelocity = if (velocityHistory.isNotEmpty()) {
                velocityHistory.lastOrNull() ?: 0f
            } else 0f

            currentPath?.let { path ->
                // 为秀丽笔计算物理状态
                val (brushState, inkProperties) = if (currentBrushType == BrushType.CALLIGRAPHY_PEN) {
                    val deltaTime = if (lastPhysicsUpdateTime > 0) {
                        (currentTime - lastPhysicsUpdateTime) / 1_000_000_000f
                    } else 0.016f

                    val brushState = calligraphyPhysics.calculateBrushTipState(
                        smoothedPressure, currentVelocity, previousBrushState, deltaTime
                    )
                    val inkProperties = calligraphyPhysics.calculateInkProperties(brushState, smoothedPressure)

                    previousBrushState = brushState
                    lastPhysicsUpdateTime = currentTime

                    Pair(brushState, inkProperties)
                } else {
                    Pair(null, null)
                }

                // 使用二次贝塞尔曲线平滑路径
                val controlX = (lastX + x) / 2
                val controlY = (lastY + y) / 2
                path.quadTo(lastX, lastY, controlX, controlY)
                // 墨水渗透笔刷需要传递停留时间和线宽信息
                val dwellTime = if (currentBrushType.needsInkPenetration()) inkPenetrationBrush.getCurrentDwellTime() else 0f
                val finalWidth = if (currentBrushType.needsInkPenetration()) inkPenetrationBrush.getCurrentWidth() else 0f

                path.addPoint(x, y, smoothedPressure, isStylus, currentBrushType, currentVelocity, brushState, inkProperties, dwellTime, finalWidth)
            }
            lastX = x
            lastY = y
            lastPressure = smoothedPressure

            // 立即绘制到UI线程
            drawOnCanvas()
        }
    }

    // 开始抬笔笔锋效果（主流软件风格）
    private fun startPenLift(x: Float, y: Float, pressure: Float, isStylus: Boolean) {
        isLiftingPen = true
        liftStartTime = System.nanoTime()
        originalLiftPressure = pressure

        // 智能判断是否应该生成笔锋（Notability风格）
        if (shouldCreatePenTip()) {
            createAdvancedPenLiftEffect(x, y, isStylus)
        } else {
            // 简单结束，不生成笔锋
            finishStrokeSimple()
        }
    }

    // 创建高级笔锋效果（结合主流软件技术）
    private fun createAdvancedPenLiftEffect(endX: Float, endY: Float, isStylus: Boolean) {
        currentPath?.let { path ->
            val startX = lastX
            val startY = lastY

            // 使用速度感知的笔锋计算（Apple Notes风格）
            val liftDirection = calculateSmartLiftDirection()
            val liftLength = calculateVelocityBasedLength()

            // 创建物理模拟的笔锋形状（Procreate风格）
            createPhysicalPenTip(path, startX, startY, liftDirection, liftLength, isStylus)

            // 完成笔画
            finishStroke(path)
        }
    }

    // 简单结束笔画（无笔锋）
    private fun finishStrokeSimple() {
        currentPath?.let { path ->
            path.lineTo(lastX, lastY)
            finishStroke(path)
        }
    }

    // 创建自然的笔锋形状
    private fun createNaturalPenTip(
        path: StrokePath,
        startX: Float,
        startY: Float,
        direction: Pair<Float, Float>,
        length: Float,
        isStylus: Boolean
    ) {
        // 使用优化的平滑曲线方法
        createSmoothPenTipCurve(path, startX, startY, direction, length, isStylus)
    }

    // 智能方向计算（结合历史数据）
    private fun calculateSmartLiftDirection(): Pair<Float, Float> {
        // 优先使用方向历史数据
        if (directionHistory.isNotEmpty()) {
            // 计算加权平均方向
            var totalX = 0f
            var totalY = 0f
            var totalWeight = 0f

            for (i in directionHistory.indices) {
                val weight = (i + 1).toFloat() // 越新的方向权重越大
                val direction = directionHistory[i]
                totalX += direction.first * weight
                totalY += direction.second * weight
                totalWeight += weight
            }

            if (totalWeight > 0) {
                val avgX = totalX / totalWeight
                val avgY = totalY / totalWeight
                val length = sqrt(avgX * avgX + avgY * avgY)

                return if (length > 0) {
                    Pair(avgX / length, avgY / length)
                } else {
                    Pair(1f, 0f) // 默认水平方向
                }
            }
        }

        // 回退到传统方法
        return calculateLiftDirection(currentPath ?: return Pair(1f, 0f))
    }

    // 传统方向计算（备用）- 线程安全版本
    private fun calculateLiftDirection(path: StrokePath): Pair<Float, Float> {
        val points = synchronized(path.points) {
            ArrayList(path.points)
        }
        if (points.size < 2) return Pair(1f, 0f)

        // 使用最后几个点计算方向
        val lookBack = minOf(3, points.size - 1)
        val lastPoint = points.last()
        val prevPoint = points[points.size - 1 - lookBack]

        val dx = lastPoint.x - prevPoint.x
        val dy = lastPoint.y - prevPoint.y
        val length = sqrt(dx * dx + dy * dy)

        return if (length > 0) {
            Pair(dx / length, dy / length)
        } else {
            Pair(1f, 0f)
        }
    }

    // 速度感知的笔锋长度计算（OneNote风格）
    private fun calculateVelocityBasedLength(): Float {
        val basePressureLength = originalLiftPressure * 15f

        // 计算速度系数
        val velocityMultiplier = if (velocityHistory.isNotEmpty()) {
            val avgVelocity = velocityHistory.average().toFloat()
            val lastVelocity = velocityHistory.lastOrNull() ?: 0f

            // 速度越快，笔锋越长
            val speedFactor = (lastVelocity / 100f).coerceIn(0.5f, 2.5f)
            val avgSpeedFactor = (avgVelocity / 80f).coerceIn(0.8f, 1.5f)

            (speedFactor + avgSpeedFactor) / 2f
        } else {
            1.0f
        }

        // 根据笔刷类型调整笔锋长度
        val brushMultiplier = currentBrushType.getTipLengthMultiplier()
        val baseLength = basePressureLength * velocityMultiplier * brushMultiplier

        return when (currentBrushType) {
            BrushType.NORMAL -> baseLength.coerceIn(4f, 25f)
            BrushType.CALLIGRAPHY_PEN -> baseLength.coerceIn(8f, 45f) // 秀丽笔笔锋更长
            BrushType.INK_PENETRATION -> baseLength.coerceIn(3f, 20f) // 墨水渗透笔笔锋较短
        }
    }

    // 物理模拟笔锋（基于真实秀丽笔物理特性）
    private fun createPhysicalPenTip(
        path: StrokePath,
        startX: Float,
        startY: Float,
        direction: Pair<Float, Float>,
        length: Float,
        isStylus: Boolean
    ) {
        // 根据笔刷类型调整步数
        val steps = when (currentBrushType) {
            BrushType.NORMAL -> 6
            BrushType.CALLIGRAPHY_PEN -> 12 // 秀丽笔需要更多步数来实现平滑的物理恢复
            BrushType.INK_PENETRATION -> 8 // 墨水渗透笔使用中等步数
        }

        // 计算惯性系数（基于速度）
        val liftVelocity = if (velocityHistory.isNotEmpty()) {
            velocityHistory.lastOrNull() ?: 0f
        } else {
            0f
        }

        for (i in 1..steps) {
            val t = i.toFloat() / steps

            // 根据笔刷类型调整物理衰减
            val physicalT = when (currentBrushType) {
                BrushType.NORMAL -> 1f - (t * t * (2f - liftVelocity / 150f))
                BrushType.CALLIGRAPHY_PEN -> {
                    // 使用物理模型计算笔锋衰减
                    if (previousBrushState != null) {
                        val tipPoints = calligraphyPhysics.calculateBrushTipEffect(
                            previousBrushState!!, originalLiftPressure, liftVelocity, direction
                        )

                        // 基于物理模型的弹性恢复
                        val elasticRecovery = previousBrushState!!.recoveryFactor
                        val naturalDecay = exp(-t * 2.5f).toFloat()
                        val physicalDecay = (1f - t * t * t) * elasticRecovery

                        (naturalDecay + physicalDecay) / 2f
                    } else {
                        // 回退到基本物理模型
                        val inkFlow = 1f - (t * t * t * (3f - 2f * liftVelocity / 150f))
                        val naturalTaper = exp(-t * 2.5f).toFloat()
                        (inkFlow + naturalTaper) / 2f
                    }
                }
                BrushType.INK_PENETRATION -> {
                    // 墨水渗透笔使用简单的线性衰减
                    1f - (t * t * (1.5f - liftVelocity / 200f))
                }
            }

            // 计算位置
            val x = startX + direction.first * length * t
            val y = startY + direction.second * length * t

            // 计算压感（物理衰减）
            val pressure = originalLiftPressure * physicalT.coerceIn(0.01f, 1f)

            // 为秀丽笔计算物理状态
            val (brushState, inkProperties) = if (currentBrushType == BrushType.CALLIGRAPHY_PEN) {
                val deltaTime = 0.016f * t // 模拟时间步长
                val brushState = calligraphyPhysics.calculateBrushTipState(
                    pressure, liftVelocity * (1f - t), previousBrushState, deltaTime
                )
                val inkProperties = calligraphyPhysics.calculateInkProperties(brushState, pressure)
                Pair(brushState, inkProperties)
            } else {
                Pair(null, null)
            }

            // 添加点
            // 笔锋效果中时间笔刷使用初始值
            val dwellTime = 0f
            val finalWidth = if (currentBrushType.needsInkPenetration()) InkPenetrationBrush.INITIAL_WIDTH else 0f

            path.addPoint(x, y, pressure, isStylus, currentBrushType, liftVelocity * (1f - t), brushState, inkProperties, dwellTime, finalWidth)

            // 平滑连接
            if (i == 1) {
                path.quadTo(startX, startY, x, y)
            } else {
                path.lineTo(x, y)
            }
        }
    }

    // 优化笔锋曲线，使其更加平滑自然
    private fun createSmoothPenTipCurve(
        path: StrokePath,
        startX: Float,
        startY: Float,
        direction: Pair<Float, Float>,
        length: Float,
        isStylus: Boolean
    ) {
        val controlPoints = mutableListOf<Pair<Float, Float>>()
        val pressurePoints = mutableListOf<Float>()

        // 生成控制点
        val steps = 6
        for (i in 1..steps) {
            val t = i.toFloat() / steps
            val shapeT = 1f - (t * t * t) // 立方衰减

            val x = startX + direction.first * length * t
            val y = startY + direction.second * length * t
            val pressure = originalLiftPressure * shapeT.coerceIn(0.05f, 1f)

            controlPoints.add(Pair(x, y))
            pressurePoints.add(pressure)
        }

        // 使用贝塞尔曲线连接控制点
        for (i in controlPoints.indices) {
            val point = controlPoints[i]
            val pressure = pressurePoints[i]

            path.addPoint(point.first, point.second, pressure, isStylus, currentBrushType, 0f)

            when (i) {
                0 -> {
                    // 第一个点：平滑过渡
                    path.quadTo(startX, startY, point.first, point.second)
                }
                controlPoints.size - 1 -> {
                    // 最后一个点：直接连接
                    path.lineTo(point.first, point.second)
                }
                else -> {
                    // 中间点：使用前一个点作为控制点的平滑曲线
                    val prevPoint = controlPoints[i - 1]
                    val controlX = (prevPoint.first + point.first) / 2
                    val controlY = (prevPoint.second + point.second) / 2
                    path.quadTo(controlX, controlY, point.first, point.second)
                }
            }
        }
    }

    // 完成笔画
    private fun finishStroke(path: StrokePath) {
        paths.add(path)
        currentPath = null
        isLiftingPen = false

        // 停止渗透笔更新
        stopPenetrationUpdate()

        onStateChangedListener?.invoke() // 通知状态变化

        // 立即绘制到UI线程
        drawOnCanvas()
    }

    private fun touchUp() {
        // 保留原有方法作为备用
        currentPath?.let { path ->
            path.lineTo(lastX, lastY)
            paths.add(path)
            onStateChangedListener?.invoke() // 通知状态变化
        }
        currentPath = null
    }

    // 清除画布
    fun clearCanvas() {
        paths.clear()
        currentPath = null
        redoStack.clear() // 清除画布时也清除重做栈

        // 清除墨水渗透笔刷状态
        inkPenetrationBrush.reset()
        isPenetrationBrushActive = false

        needsRedraw = true
        onStateChangedListener?.invoke() // 通知状态变化
    }

    // 撤销最后一笔
    fun undoLastStroke() {
        if (paths.isNotEmpty()) {
            val lastPath = paths.removeAt(paths.size - 1)
            redoStack.add(lastPath) // 将撤销的笔画添加到重做栈
            needsRedraw = true
            onStateChangedListener?.invoke() // 通知状态变化
        }
    }

    // 重做最后一次撤销的笔画
    fun redoLastStroke() {
        if (redoStack.isNotEmpty()) {
            val pathToRestore = redoStack.removeAt(redoStack.size - 1)
            paths.add(pathToRestore) // 将笔画从重做栈恢复到主画布
            needsRedraw = true
            onStateChangedListener?.invoke() // 通知状态变化
        }
    }

    // 检查是否可以撤销
    fun canUndo(): Boolean = paths.isNotEmpty()

    // 检查是否可以重做
    fun canRedo(): Boolean = redoStack.isNotEmpty()

    // 设置FPS更新监听器
    fun setFpsUpdateListener(listener: (Float) -> Unit) {
        fpsUpdateListener = listener
    }

    // 设置事件频率更新监听器
    fun setEventRateUpdateListener(listener: (Float, Float) -> Unit) {
        eventRateUpdateListener = listener
    }

    // 获取当前FPS
    fun getCurrentFps(): Float = currentFps

    // 获取当前事件频率
    fun getCurrentEventRates(): Pair<Float, Float> = Pair(currentStylusEventRate, currentFingerEventRate)

    // 获取性能统计信息
    fun getPerformanceStats(): String {
        val efficiency = if (totalFrames > 0) {
            ((totalFrames - droppedFrames) * 100.0 / totalFrames)
        } else 0.0
        return "效率: %.1f%% | 总帧: %d | 丢帧: %d".format(efficiency, totalFrames, droppedFrames)
    }

    // 记录输入事件
    private fun recordInputEvent(isStylus: Boolean, actionType: Int) {
        if (isStylus) {
            stylusEventCount++
            when (actionType) {
                MotionEvent.ACTION_DOWN -> stylusDownCount++
                MotionEvent.ACTION_MOVE -> stylusMoveCount++
                MotionEvent.ACTION_UP -> stylusUpCount++
            }
        } else {
            fingerEventCount++
            when (actionType) {
                MotionEvent.ACTION_DOWN -> fingerDownCount++
                MotionEvent.ACTION_MOVE -> fingerMoveCount++
                MotionEvent.ACTION_UP -> fingerUpCount++
            }
        }

        val currentTime = System.nanoTime()
        if (lastEventStatsTime == 0L) {
            lastEventStatsTime = currentTime
        }

        // 每秒更新一次事件频率统计
        if (currentTime - lastEventStatsTime >= 1_000_000_000L) {
            val timeElapsed = currentTime - lastEventStatsTime
            currentStylusEventRate = stylusEventCount * 1_000_000_000.0f / timeElapsed
            currentFingerEventRate = fingerEventCount * 1_000_000_000.0f / timeElapsed

            // 重置计数器
            stylusEventCount = 0
            fingerEventCount = 0
            stylusDownCount = 0
            stylusMoveCount = 0
            stylusUpCount = 0
            fingerDownCount = 0
            fingerMoveCount = 0
            fingerUpCount = 0
            lastEventStatsTime = currentTime

            // 通知事件频率更新
            eventRateUpdateListener?.invoke(currentStylusEventRate, currentFingerEventRate)
        }
    }

    // 获取详细事件统计信息
    fun getEventStats(): String {
        return "手写笔: D:$stylusDownCount M:$stylusMoveCount U:$stylusUpCount | 手指: D:$fingerDownCount M:$fingerMoveCount U:$fingerUpCount"
    }

    // 压感平滑算法
    private fun smoothPressure(newPressure: Float): Float {
        // 应用压感敏感度放大
        val amplifiedPressure = (newPressure * pressureSensitivity).coerceIn(0f, 1f)

        // 添加新的压感值到历史记录
        pressureHistory.add(amplifiedPressure)

        // 保持历史记录大小限制
        if (pressureHistory.size > maxPressureHistorySize) {
            pressureHistory.removeAt(0)
        }

        // 使用加权移动平均算法
        var weightedSum = 0f
        var totalWeight = 0f

        for (i in pressureHistory.indices) {
            // 越新的数据权重越大
            val weight = (i + 1).toFloat()
            weightedSum += pressureHistory[i] * weight
            totalWeight += weight
        }

        return weightedSum / totalWeight
    }

    // 设置压感敏感度
    fun setPressureSensitivity(sensitivity: Float) {
        pressureSensitivity = sensitivity.coerceIn(0.5f, 3.0f)
    }

    // 笔刷类型管理方法
    fun setBrushType(brushType: BrushType) {
        if (currentBrushType != brushType) {
            // 切换笔刷时清理墨水渗透笔刷的状态
            if (currentBrushType == BrushType.INK_PENETRATION) {
                inkPenetrationBrush.clearPenetrationState()
                isPenetrationBrushActive = false
            }

            currentBrushType = brushType
            // 根据笔刷类型调整压感敏感度
            pressureSensitivity = brushType.pressureSensitivity
            brushChangeListener?.invoke(brushType)
        }
    }

    fun getCurrentBrushType(): BrushType = currentBrushType

    fun setBrushChangeListener(listener: (BrushType) -> Unit) {
        brushChangeListener = listener
    }

    fun getPenetrationInfo(): String {
        return inkPenetrationBrush.getPenetrationInfo()
    }

    // 更新速度和方向历史（OneNote风格）
    private fun updateVelocityAndDirection(x: Float, y: Float, currentTime: Long) {
        if (lastMoveTime > 0) {
            val deltaTime = (currentTime - lastMoveTime) / 1_000_000f // 转换为毫秒
            val distance = sqrt((x - lastX) * (x - lastX) + (y - lastY) * (y - lastY))
            val velocity = if (deltaTime > 0) distance / deltaTime else 0f

            // 添加速度到历史
            velocityHistory.add(velocity)
            if (velocityHistory.size > maxHistorySize) {
                velocityHistory.removeAt(0)
            }

            // 添加方向到历史
            val length = sqrt((x - lastX) * (x - lastX) + (y - lastY) * (y - lastY))
            if (length > 0) {
                val dirX = (x - lastX) / length
                val dirY = (y - lastY) / length
                directionHistory.add(Pair(dirX, dirY))
                if (directionHistory.size > maxHistorySize) {
                    directionHistory.removeAt(0)
                }
            }
        }
        lastMoveTime = currentTime
    }

    // 智能判断是否应该生成笔锋（Notability风格）
    private fun shouldCreatePenTip(): Boolean {
        // 检查压感是否足够
        if (originalLiftPressure < 0.2f) return false

        // 检查速度历史
        if (velocityHistory.isEmpty()) return false

        val avgVelocity = velocityHistory.average().toFloat()
        val lastVelocity = velocityHistory.lastOrNull() ?: 0f

        // 快速抬笔或中等速度时生成笔锋
        return lastVelocity > 50f || (avgVelocity > 30f && originalLiftPressure > 0.3f)
    }

    // 获取设备刷新率
    private fun getDisplayRefreshRate() {
        try {
            val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
            val display = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                // Android 11+ (API 30+)
                context.display
            } else {
                // Android 10 及以下版本
                @Suppress("DEPRECATION")
                windowManager.defaultDisplay
            }

            displayRefreshRate = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                // Android 6.0+ (API 23+) 支持多种刷新率
                val currentMode = display?.mode
                currentMode?.refreshRate ?: 60.0f
            } else {
                // 旧版本Android
                @Suppress("DEPRECATION")
                display?.refreshRate ?: 60.0f
            }
        } catch (e: Exception) {
            // 如果获取刷新率失败，使用默认值
            displayRefreshRate = 60.0f
        }
    }

    // DrawingThread类已移除，改为UI线程绘制

    // 移除的DrawingThread类功能已整合到drawOnCanvas方法中







    // 移除DrawingThread中的重复方法，使用主类中的方法










    // 数据类：存储笔画路径和压感信息
    private data class StrokePoint(
        val x: Float,
        val y: Float,
        val pressure: Float,
        val isStylus: Boolean = false,
        val brushType: BrushType = BrushType.NORMAL,
        val velocity: Float = 0f,
        val brushState: CalligraphyPenPhysics.BrushTipState? = null,
        val inkProperties: CalligraphyPenPhysics.InkProperties? = null,
        var dwellTime: Float = 0f,  // 停留时间（用于时间笔刷，可变）
        var finalWidth: Float = 0f  // 最终线宽（用于时间笔刷，可变）
    )

    private class StrokePath : Path() {
        val points = mutableListOf<StrokePoint>()
        var brushType: BrushType = BrushType.NORMAL

        fun addPoint(
            x: Float,
            y: Float,
            pressure: Float,
            isStylus: Boolean = false,
            brushType: BrushType = BrushType.NORMAL,
            velocity: Float = 0f,
            brushState: CalligraphyPenPhysics.BrushTipState? = null,
            inkProperties: CalligraphyPenPhysics.InkProperties? = null,
            dwellTime: Float = 0f,
            finalWidth: Float = 0f
        ) {
            // 移除同步代码，因为现在在UI线程上运行
            points.add(StrokePoint(x, y, pressure, isStylus, brushType, velocity, brushState, inkProperties, dwellTime, finalWidth))
        }
    }
}
