package com.example.brushapp

import kotlin.math.*

/**
 * 真实秀丽笔物理建模
 * 基于真实秀丽笔的纤维结构、压力响应和墨水流动特性
 */
class CalligraphyPenPhysics {
    
    // 笔头物理参数（基于真实秀丽笔测量）
    companion object {
        // 笔头几何参数
        const val TIP_DIAMETER = 0.8f        // 笔尖直径 (mm)
        const val MAX_CONTACT_DIAMETER = 4.5f // 最大接触直径 (mm)
        const val FIBER_LENGTH = 8.0f        // 纤维长度 (mm)
        const val FIBER_STIFFNESS = 0.15f    // 纤维刚度系数
        
        // 压力响应参数
        const val MIN_PRESSURE = 0.02f       // 最小有效压力
        const val MAX_PRESSURE = 1.0f        // 最大压力
        const val PRESSURE_SENSITIVITY = 2.8f // 压力敏感度
        
        // 墨水流动参数
        const val INK_VISCOSITY = 0.8f       // 墨水粘度
        const val PAPER_ABSORPTION = 0.3f    // 纸张吸收率
        const val CAPILLARY_ACTION = 0.25f   // 毛细作用强度
        
        // 弹性恢复参数
        const val ELASTIC_MODULUS = 0.6f     // 弹性模量
        const val RECOVERY_TIME = 0.15f      // 恢复时间常数 (秒)
    }
    
    // 笔头状态
    data class BrushTipState(
        val contactRadius: Float,           // 接触半径
        val deformation: Float,             // 变形程度 (0-1)
        val inkFlow: Float,                 // 墨水流量
        val elasticStrain: Float,           // 弹性应变
        val recoveryFactor: Float           // 恢复因子
    )
    
    // 墨水特性
    data class InkProperties(
        val density: Float,                 // 密度
        val transparency: Float,            // 透明度
        val diffusionRadius: Float,         // 扩散半径
        val saturation: Float               // 饱和度
    )
    
    /**
     * 计算笔头在给定压力下的物理状态
     * 基于真实纤维材料的非线性响应
     */
    fun calculateBrushTipState(
        pressure: Float,
        velocity: Float,
        previousState: BrushTipState? = null,
        deltaTime: Float = 0.016f // 60fps
    ): BrushTipState {
        val normalizedPressure = pressure.coerceIn(MIN_PRESSURE, MAX_PRESSURE)
        
        // 1. 计算纤维变形（非线性响应）
        val deformation = calculateFiberDeformation(normalizedPressure)
        
        // 2. 计算接触面积（基于变形的椭圆接触）
        val contactRadius = calculateContactRadius(deformation, velocity)
        
        // 3. 计算墨水流量（压力和接触面积的函数）
        val inkFlow = calculateInkFlow(normalizedPressure, contactRadius, velocity)
        
        // 4. 计算弹性应变和恢复
        val (elasticStrain, recoveryFactor) = calculateElasticResponse(
            normalizedPressure, previousState, deltaTime
        )
        
        return BrushTipState(
            contactRadius = contactRadius,
            deformation = deformation,
            inkFlow = inkFlow,
            elasticStrain = elasticStrain,
            recoveryFactor = recoveryFactor
        )
    }
    
    /**
     * 计算纤维变形
     * 基于真实纤维材料的非线性压缩特性
     */
    private fun calculateFiberDeformation(pressure: Float): Float {
        // 使用修正的Hertz接触理论
        // 考虑纤维束的非均匀性和各向异性
        
        val pressureRatio = pressure / MAX_PRESSURE
        
        return when {
            pressureRatio <= 0.1f -> {
                // 初始接触阶段：纤维尖端轻微变形
                val t = pressureRatio / 0.1f
                0.05f * t * t
            }
            pressureRatio <= 0.4f -> {
                // 压缩阶段：纤维开始显著变形
                val t = (pressureRatio - 0.1f) / 0.3f
                0.05f + 0.25f * (1f - exp(-3f * t))
            }
            pressureRatio <= 0.8f -> {
                // 塑性变形阶段：纤维大幅压扁
                val t = (pressureRatio - 0.4f) / 0.4f
                0.3f + 0.5f * (t * t * (3f - 2f * t)) // 平滑三次曲线
            }
            else -> {
                // 饱和阶段：接近最大变形
                val t = (pressureRatio - 0.8f) / 0.2f
                0.8f + 0.15f * (1f - exp(-5f * t))
            }
        }
    }
    
    /**
     * 计算接触半径
     * 考虑速度对接触形状的影响
     */
    private fun calculateContactRadius(deformation: Float, velocity: Float): Float {
        val baseRadius = TIP_DIAMETER / 2f + 
                        (MAX_CONTACT_DIAMETER / 2f - TIP_DIAMETER / 2f) * deformation
        
        // 速度影响：高速时接触面积略微减小（动态效应）
        val velocityFactor = 1f - 0.1f * tanh(velocity / 100f)
        
        return baseRadius * velocityFactor
    }
    
    /**
     * 计算墨水流量
     * 基于压力驱动流动和毛细作用
     */
    private fun calculateInkFlow(pressure: Float, contactRadius: Float, velocity: Float): Float {
        // Poiseuille流动方程的简化版本
        val pressureDrivenFlow = pressure * pressure / INK_VISCOSITY
        
        // 毛细作用（接触面积越大，毛细作用越强）
        val capillaryFlow = CAPILLARY_ACTION * contactRadius * contactRadius
        
        // 速度影响（快速移动时墨水流动增加）
        val velocityBoost = 1f + 0.3f * tanh(velocity / 50f)
        
        return (pressureDrivenFlow + capillaryFlow) * velocityBoost
    }
    
    /**
     * 计算弹性响应和恢复
     */
    private fun calculateElasticResponse(
        pressure: Float,
        previousState: BrushTipState?,
        deltaTime: Float
    ): Pair<Float, Float> {
        val currentStrain = pressure * FIBER_STIFFNESS
        
        val elasticStrain = if (previousState != null) {
            // 考虑弹性恢复的时间延迟
            val targetStrain = currentStrain
            val currentStrain = previousState.elasticStrain
            val recoveryRate = 1f - exp(-deltaTime / RECOVERY_TIME)
            
            currentStrain + (targetStrain - currentStrain) * recoveryRate
        } else {
            currentStrain
        }
        
        // 恢复因子：压力释放时的恢复程度
        val recoveryFactor = if (pressure < MIN_PRESSURE * 2f) {
            min(1f, (previousState?.recoveryFactor ?: 0f) + deltaTime / RECOVERY_TIME)
        } else {
            0f
        }
        
        return Pair(elasticStrain, recoveryFactor)
    }
    
    /**
     * 计算墨水在纸张上的扩散特性
     */
    fun calculateInkProperties(
        brushState: BrushTipState,
        pressure: Float,
        paperTexture: Float = 1f
    ): InkProperties {
        val inkFlow = brushState.inkFlow
        val contactRadius = brushState.contactRadius
        
        // 墨水密度（流量和接触面积的函数）
        val density = (inkFlow / (contactRadius * contactRadius)).coerceIn(0.3f, 1f)
        
        // 透明度（密度的反函数，但有最小值）
        val transparency = (1f - density * 0.7f).coerceIn(0.2f, 0.8f)
        
        // 扩散半径（基于毛细作用和纸张纹理）
        val baseDiffusion = contactRadius * (1f + PAPER_ABSORPTION * paperTexture)
        val diffusionRadius = baseDiffusion * sqrt(inkFlow)
        
        // 饱和度（压力和墨水流量的函数）
        val saturation = (pressure * inkFlow * 2f).coerceIn(0.6f, 1f)
        
        return InkProperties(
            density = density,
            transparency = transparency,
            diffusionRadius = diffusionRadius,
            saturation = saturation
        )
    }
    
    /**
     * 计算笔锋效果的物理参数
     */
    fun calculateBrushTipEffect(
        startState: BrushTipState,
        endPressure: Float,
        liftVelocity: Float,
        direction: Pair<Float, Float>
    ): List<Pair<Float, Float>> {
        val steps = 8
        val tipPoints = mutableListOf<Pair<Float, Float>>()
        
        for (i in 1..steps) {
            val t = i.toFloat() / steps
            
            // 物理衰减：考虑纤维弹性恢复
            val pressureDecay = endPressure * exp(-t * 3f) * startState.recoveryFactor
            val radiusDecay = startState.contactRadius * exp(-t * 2f)
            
            // 惯性效应：快速抬笔时笔锋更长
            val inertiaFactor = 1f + liftVelocity / 200f
            val distance = radiusDecay * 3f * inertiaFactor * t
            
            val x = direction.first * distance
            val y = direction.second * distance
            
            tipPoints.add(Pair(x, y))
        }
        
        return tipPoints
    }
}
