<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainActivity">

    <!-- 刷新率显示 -->
    <TextView
        android:id="@+id/textViewRefreshRate"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/refresh_rate_loading"
        android:textSize="14sp"
        android:textColor="@color/purple_700"
        android:background="@color/white"
        android:padding="8dp"
        android:layout_margin="8dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- 手写SurfaceView占据大部分屏幕 -->
    <com.example.brushapp.HandwritingSurfaceView
        android:id="@+id/handwritingSurfaceView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="8dp"
        app:layout_constraintTop_toBottomOf="@+id/textViewRefreshRate"
        app:layout_constraintBottom_toTopOf="@+id/buttonContainer"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- 底部按钮容器 -->
    <LinearLayout
        android:id="@+id/buttonContainer"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:padding="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <Button
            android:id="@+id/buttonClear"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/button_clear"
            android:layout_marginEnd="8dp"
            android:backgroundTint="@color/teal_700" />

        <Button
            android:id="@+id/buttonBrushType"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/button_normal_brush"
            android:backgroundTint="@color/teal_200" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
