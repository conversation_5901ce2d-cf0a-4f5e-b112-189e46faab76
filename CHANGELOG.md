# 更新日志

本文档记录了Brush App的所有重要更新和改进。

## [v2.0.0] - 2025-01-14 - 墨水渗透物理模型重大升级 (完全重构版)

### 🚀 重大功能更新

#### 真实墨水渗透物理引擎
- **InkPenetrationEngine**: 全新的墨水渗透引擎，基于真实物理原理
- **Fick扩散定律**: 实现分子级的墨水扩散模拟
- **Lucas-Washburn方程**: 模拟纸张纤维的毛细作用效应
- **达西定律**: 模拟墨水在多孔介质中的流动行为
- **纸张特性建模**: 考虑孔隙率、粗糙度、吸收率的局部变化

#### 完全重构的墨水渗透笔刷 (InkPenetrationBrush)
- **类名更新**: TimeDilationBrush → InkPenetrationBrush，更准确反映物理本质
- **枚举更新**: BrushType.TIME_DILATION → BrushType.INK_PENETRATION
- **移除传统算法**: 完全抛弃简化的时间累积模型
- **纯物理驱动**: 100%基于Fick扩散定律、Lucas-Washburn毛细作用和达西流动
- **智能渗透点管理**: 动态创建和增强渗透点，最大8个同时活跃
- **敏感停留检测**: 3像素移动阈值，更精确的停留识别
- **强度动态调整**: 基于停留时间的渐进式强度增长
- **自然衰减**: 停止绘制后渗透效果自然演化和衰减

#### 视觉效果革新
- **物理浓度渲染**: 直接基于InkPenetrationEngine的浓度计算
- **增强边缘模糊**: 0.2浓度阈值触发，0.8-4像素动态模糊半径
- **真实墨水色彩**: 更深的颜色变化和密度差异模拟
- **微妙色彩偏移**: 高浓度区域的红蓝色调变化
- **降低基础透明度**: 让渗透效果更加明显和自然

### 🔧 技术架构改进

#### 新增核心组件
- **InkPenetrationEngine**: 墨水渗透物理引擎
- **PenetrationPoint**: 渗透点数据结构
- **PaperRegion**: 纸张特性建模
- **InkPenetrationBrush**: 完全重构的墨水渗透笔刷

#### 性能优化
- **网格化管理**: 10×10像素网格优化纸张特性计算
- **活跃点限制**: 最大10个同时渗透点，防止性能下降
- **距离预筛选**: 渗透计算前的距离检查优化
- **智能重绘**: 只在渗透状态变化时触发重绘

#### 数值稳定性
- **边界条件处理**: 完善的边界值检查和限制
- **数值收敛**: 确保长时间运行的数值稳定性
- **内存管理**: 自动清理和对象复用机制

### 📚 文档更新

#### 新增技术文档
- **INK_PENETRATION_MODEL.md**: 墨水渗透模型详细技术文档
- **INK_PENETRATION_USAGE_GUIDE.md**: 使用指南和最佳实践
- **更新TIME_DILATION_BRUSH.md**: 添加v2.0物理模型说明

#### 测试覆盖
- **InkPenetrationEngineTest**: 完整的单元测试套件
- **物理模型验证**: 数值稳定性和边界条件测试
- **性能基准测试**: 确保实时性能要求

### 🔄 重大变更 (Breaking Changes)

#### 完全重构
- **移除传统算法**: 不再保持向后兼容，完全基于物理模型
- **API简化**: 移除传统的时间累积参数和方法
- **性能优化**: 去除冗余的兼容性代码，提升执行效率
- **命名更新**: "时间笔" → "渗透笔"，TimeDilationBrush → InkPenetrationBrush

#### 新的核心特性
- **纯物理驱动**: 所有效果完全基于真实物理计算
- **智能参数**: 自动优化的物理参数，无需手动调节
- **增强调试**: 更详细的渗透状态信息和性能监控

---

## [v1.2.0] - 2025-01-12 - 时间膨胀笔刷创新功能

### 🎨 新增功能

#### 时间膨胀笔刷系统
- **创新交互模式**：笔画初始线宽较细，随停留时间逐渐增粗
- **时间累积算法**：基于S型曲线的自然膨胀强度变化
- **影响范围扩散**：25像素半径内的渐变膨胀效果
- **距离衰减计算**：平滑的边缘过渡和自然的强度分布
- **实时动态更新**：60fps的流畅膨胀效果

#### 高级视觉效果
- **动态线宽**：1.5-12像素的膨胀范围（最大8倍增长）
- **颜色深度变化**：膨胀区域颜色加深，模拟墨水浓度增加
- **脉冲效果**：轻微的周期性强度变化增加生动感
- **渐变边缘**：平滑的膨胀边界和自然过渡

#### 用户界面改进
- **三笔刷循环**：普通笔→秀丽笔→时间笔的循环切换
- **动态按钮颜色**：时间笔使用深青色(teal_700)标识
- **实时状态反馈**：即时的视觉响应和状态显示

### 🔧 技术改进

#### 核心架构
- **新增 TimeDilationBrush 类**：专门的时间膨胀引擎
- **扩展 BrushType 枚举**：支持TIME_DILATION类型
- **优化 HandwritingSurfaceView**：集成时间追踪和动态更新

#### 性能优化
- **智能更新频率**：60fps的膨胀效果更新，避免过度重绘
- **内存管理优化**：自动清理非活跃膨胀点，限制同时存在数量
- **计算效率提升**：距离计算优化和预计算常用函数值

#### 数据结构扩展
- **DilationPoint**：完整的膨胀点状态描述
- **DilationState**：全局膨胀状态管理
- **实时状态追踪**：活跃膨胀点监控和调试信息

### 📊 物理参数

#### 时间膨胀参数
```
初始线宽: 1.5像素
最大膨胀线宽: 12像素
影响半径: 25像素
时间阈值: 0.1秒
最大膨胀时间: 3.0秒
距离衰减因子: 0.8
脉冲频率: 2.0Hz
脉冲幅度: 0.1
```

#### 响应特性
- **膨胀倍数**：最大8倍线宽增长
- **响应曲线**：S型平滑过渡
- **更新频率**：60fps实时更新

### 🎯 使用场景
- **艺术创作**：表现性绘画和抽象艺术
- **设计应用**：Logo设计和插画创作
- **教育用途**：物理演示和艺术教学
- **实验性作品**：探索时间与空间的关系

### 📚 文档更新
- **TIME_DILATION_BRUSH.md**：详细的时间膨胀笔刷技术文档
- **README.md**：更新项目介绍包含新笔刷类型
- **用户指南**：添加时间膨胀笔刷使用说明

---

## [v1.1.0] - 2025-01-12 - 秀丽笔物理建模重大更新

### 🎨 新增功能

#### 秀丽笔物理建模系统
- **真实物理特性建模**：基于真实秀丽笔的纤维结构、压力响应和墨水流动特性
- **四阶段压力响应**：初始接触、压缩、塑性变形、饱和阶段的精确模拟
- **非线性变形算法**：基于Hertz接触理论的纤维笔头变形计算
- **墨水流动模拟**：集成Poiseuille流动方程和毛细作用效应
- **弹性恢复系统**：模拟真实纤维材料的粘弹性特性

#### 高级视觉效果
- **动态透明度**：基于墨水密度的真实透明度计算（120-255范围）
- **颜色深度变化**：根据压力和墨水浓度动态调整颜色
- **边缘扩散效果**：基于毛细作用的自然墨水扩散模拟
- **物理笔锋**：12步物理衰减算法，模拟真实的笔锋效果

#### 用户界面改进
- **笔刷切换按钮**：一键在普通笔和秀丽笔之间切换
- **动态按钮状态**：不同笔刷类型显示不同颜色
- **实时状态反馈**：显示当前选中的笔刷类型

### 🔧 技术改进

#### 核心架构
- **新增 CalligraphyPenPhysics 类**：专门的物理建模引擎
- **扩展 BrushType 枚举**：支持多种笔刷类型和属性
- **优化 HandwritingSurfaceView**：集成物理计算和高级渲染

#### 性能优化
- **实时物理计算**：60fps的物理状态更新
- **智能渲染段数**：根据笔刷类型调整渲染精度（普通笔5-8段，秀丽笔12段）
- **内存管理优化**：高效的物理状态缓存和复用

#### 数据结构扩展
- **StrokePoint 增强**：添加速度、物理状态和墨水属性
- **BrushTipState**：完整的笔头物理状态描述
- **InkProperties**：详细的墨水特性参数

### 📊 物理参数

#### 真实测量参数
```
笔尖直径: 0.8mm
最大接触直径: 4.5mm  
纤维长度: 8.0mm
纤维刚度: 0.15
墨水粘度: 0.8
弹性模量: 0.6
恢复时间: 0.15秒
```

#### 压感响应范围
- **普通笔刷**：2-8像素，线性响应
- **秀丽笔**：0.8-15像素，非线性物理响应

### 🐛 修复问题
- 修复了压感数据的平滑处理算法
- 解决了高速绘画时的线条断裂问题
- 优化了内存使用，减少了长时间使用后的性能下降
- 修复了撤销/重做功能的状态同步问题

### 📚 文档更新
- **README.md**：完整的项目介绍和使用说明
- **技术文档**：详细的物理建模实现文档
- **用户指南**：全面的使用教程和技巧说明
- **更新日志**：完整的版本历史记录

---

## [v1.0.0] - 2025-01-10 - 初始版本

### 🎨 基础功能

#### 绘画系统
- **基础绘画功能**：支持手指和手写笔输入
- **压感支持**：基础的压力感应和线条宽度调整
- **平滑曲线**：使用二次贝塞尔曲线的路径平滑
- **实时渲染**：独立绘制线程确保流畅性

#### 用户界面
- **清除功能**：一键清空画布
- **撤销/重做**：支持多步操作撤销
- **性能监控**：实时显示FPS和刷新率信息

#### 技术架构
- **SurfaceView渲染**：高性能的绘制系统
- **多线程架构**：分离UI和绘制线程
- **内存优化**：高效的路径存储和管理

### 🔧 核心组件

#### HandwritingSurfaceView
- 主要绘画视图组件
- 触摸事件处理和压感数据管理
- 绘画线程和渲染循环

#### MainActivity
- 用户界面管理
- 按钮事件处理
- 性能信息显示

### 📊 性能特性
- **高刷新率支持**：自动适配设备刷新率
- **压感平滑**：5点移动平均算法
- **内存效率**：优化的路径存储结构

### 🎯 设计目标
- 提供流畅的绘画体验
- 支持压感输入设备
- 保持高性能和低延迟
- 简洁直观的用户界面

---

## 开发计划

### 即将推出的功能 (v1.2.0)
- [ ] 作品保存和导出功能
- [ ] 更多笔刷类型（铅笔、马克笔等）
- [ ] 颜色选择器
- [ ] 图层支持
- [ ] 自定义笔刷参数

### 长期规划 (v2.0.0)
- [ ] 矢量图形支持
- [ ] 云端同步
- [ ] 协作绘画
- [ ] AI辅助功能
- [ ] 3D笔刷效果

---

## 技术债务和已知问题

### 当前限制
1. **保存功能缺失**：暂不支持作品保存
2. **颜色单一**：仅支持黑色绘画
3. **撤销限制**：只能按笔画撤销，不支持部分撤销
4. **内存累积**：长时间使用可能导致内存占用增加

### 性能考虑
1. **复杂场景**：大量路径时可能影响性能
2. **设备兼容性**：低端设备可能出现卡顿
3. **电池消耗**：物理计算增加了CPU使用

### 改进方向
1. **算法优化**：进一步优化物理计算效率
2. **渲染优化**：实现更智能的渲染策略
3. **内存管理**：改进长期使用的内存稳定性
4. **用户体验**：添加更多个性化设置选项

---

## 贡献者

感谢所有为这个项目做出贡献的开发者和用户！

### 主要贡献
- **物理建模系统**：基于真实秀丽笔特性的完整建模
- **性能优化**：多线程渲染和内存管理优化
- **用户体验**：直观的界面设计和交互逻辑

### 特别感谢
- 书法爱好者提供的真实秀丽笔使用反馈
- 测试用户的详细bug报告和改进建议
- 开源社区的技术支持和代码审查

---

**注意**：版本号遵循语义化版本控制规范 (Semantic Versioning)
- 主版本号：不兼容的API修改
- 次版本号：向下兼容的功能性新增
- 修订号：向下兼容的问题修正
