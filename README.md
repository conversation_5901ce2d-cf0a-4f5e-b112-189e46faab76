# Brush App - 智能绘画应用

一个基于Android平台的高级绘画应用，支持多种笔刷类型，特别是基于真实物理建模的秀丽笔功能。

## 功能特性

### 🎨 笔刷类型

#### 1. 普通笔刷
- 适合日常书写和绘画
- 线性压感响应
- 稳定的线条宽度控制
- 支持手写笔和手指输入

#### 2. 秀丽笔（基于物理建模）
- **真实物理特性**：基于真实秀丽笔的纤维结构建模
- **非线性压感**：四阶段压力响应曲线
- **墨水流动**：模拟真实墨水的粘度和毛细作用
- **弹性恢复**：笔头材料的真实弹性特性
- **动态笔锋**：基于物理衰减的自然笔锋效果

#### 3. 墨水渗透笔（物理模拟）
- **真实物理模型**：基于Fick扩散定律和Lucas-Washburn毛细作用
- **戏剧性对比**：初始线宽0.8px，最大可达20倍增长
- **停留渗透**：停留时间越长，墨水渗透越明显
- **浓度计算**：基于物理浓度的高级视觉效果
- **压感结合**：压感影响基础线宽，渗透影响最终效果

### 📱 用户界面

- **实时性能监控**：显示FPS、刷新率和输入事件频率
- **笔刷切换**：循环切换三种笔刷类型（普通笔→秀丽笔→渗透笔）
- **撤销/重做**：支持多步操作撤销
- **清除画布**：快速清空当前绘画

### ⚡ 性能优化

- **高刷新率支持**：自动适配设备刷新率
- **UI线程绘制**：简化架构，移除多线程复杂性
- **即时响应**：触摸事件后立即绘制
- **压感平滑**：智能压感数据处理
- **内存优化**：高效的路径存储和管理
- **并发安全**：解决了所有并发修改异常

## 技术架构

### 核心组件

1. **HandwritingSurfaceView**
   - 主要绘画视图组件
   - 处理触摸事件和压感数据
   - UI线程直接绘制，简化架构

2. **BrushType**
   - 笔刷类型枚举和管理
   - 压感响应曲线定义
   - 颜色和透明度计算

3. **CalligraphyPenPhysics**
   - 秀丽笔物理建模引擎
   - 纤维变形算法
   - 墨水流动模拟

4. **InkPenetrationBrush & InkPenetrationEngine**
   - 墨水渗透笔刷引擎
   - 基于物理模型的渗透计算
   - 浓度分布和线宽倍数算法

### 物理建模详解

#### 纤维笔头模型
```
笔尖直径: 0.8mm
最大接触直径: 4.5mm
纤维长度: 8.0mm
纤维刚度: 0.15
```

#### 压力响应阶段
1. **初始接触** (0-10%): 纤维尖端轻微变形
2. **压缩阶段** (10-40%): 纤维显著变形
3. **塑性变形** (40-80%): 纤维大幅压扁
4. **饱和阶段** (80-100%): 接近最大变形

#### 墨水流动方程
- **压力驱动流**: 基于Poiseuille方程
- **毛细作用**: 接触面积相关的毛细流动
- **速度影响**: 动态书写的流动增强

## 使用说明

### 基本操作

1. **启动应用**：打开后默认使用普通笔刷
2. **切换笔刷**：点击底部"普通笔"/"秀丽笔"按钮
3. **绘画**：在画布上直接绘画，支持压感
4. **撤销/重做**：使用对应按钮进行操作
5. **清除**：点击清除按钮清空画布

### 秀丽笔使用技巧

- **轻压**：产生细线条，适合细节描绘
- **中压**：快速变粗，适合主要笔画
- **重压**：最粗效果，适合强调和填充
- **抬笔**：自然的笔锋效果，模拟真实书法

### 性能监控

应用顶部显示实时性能信息：
- **显示刷新率**：设备屏幕刷新频率
- **绘制FPS**：实际绘制帧率
- **输入频率**：手写笔和手指事件频率

## 开发信息

### 技术栈
- **语言**: Kotlin
- **平台**: Android (API 24+)
- **架构**: MVVM + 自定义View
- **渲染**: Canvas + SurfaceView

### 依赖库
```gradle
implementation 'androidx.core:core-ktx:1.12.0'
implementation 'androidx.appcompat:appcompat:1.6.1'
implementation 'com.google.android.material:material:1.10.0'
implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
```

### 构建要求
- Android Studio Arctic Fox+
- Gradle 8.0+
- Kotlin 1.8+
- 最低API级别: 24 (Android 7.0)
- Java 17+ (推荐使用brew安装的OpenJDK)

### 构建命令
```bash
# 设置Java环境（macOS with brew）
export JAVA_HOME=/opt/homebrew/opt/openjdk@17
export PATH=$JAVA_HOME/bin:$PATH

# 构建APK
./gradlew assembleDebug -x test

# 完整构建（包含测试）
./gradlew build
```

## 文件结构

```
app/src/main/java/com/example/brushapp/
├── MainActivity.kt              # 主活动
├── HandwritingSurfaceView.kt    # 绘画视图（UI线程绘制）
├── BrushType.kt                 # 笔刷类型定义
├── CalligraphyPenPhysics.kt     # 秀丽笔物理建模引擎
├── InkPenetrationBrush.kt       # 墨水渗透笔刷
└── InkPenetrationEngine.kt      # 墨水渗透物理引擎

app/src/main/res/
├── layout/
│   └── activity_main.xml        # 主界面布局
├── values/
│   ├── strings.xml              # 字符串资源
│   └── colors.xml               # 颜色资源
└── drawable/                    # 图标资源

docs/                            # 项目文档
├── CHANGELOG.md                 # 更新日志
├── REMOVE_MULTITHREADING.md     # 多线程移除说明
├── INK_PENETRATION_*.md         # 渗透笔相关文档
└── COMPILATION_FIX.md           # 编译修复说明
```

## 版本历史

### v2.0.0 - 架构重构与渗透笔增强 (2025-01-14)
- 🔧 **移除多线程代码**：改为UI线程绘制，简化架构
- 🎨 **墨水渗透笔增强**：初始线宽降至0.8px，最大20倍对比
- 🛠️ **并发异常修复**：解决ConcurrentModificationException
- 📊 **物理参数优化**：大幅增强渗透效果的视觉对比
- ✅ **编译环境优化**：支持Java 17，修复所有编译错误

### v1.1.0 - 秀丽笔物理建模
- ✅ 添加基于真实物理特性的秀丽笔
- ✅ 实现纤维笔头变形算法
- ✅ 集成墨水流动模拟
- ✅ 添加笔头弹性恢复系统
- ✅ 优化视觉效果和用户体验

### v1.0.0 - 基础版本
- ✅ 基本绘画功能
- ✅ 压感支持
- ✅ 撤销/重做功能
- ✅ 性能监控

## 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 联系方式

如有问题或建议，请通过以下方式联系：
- 创建GitHub Issue
- 发送邮件至项目维护者

---

**注意**:
- 秀丽笔和墨水渗透笔功能基于真实物理建模，为获得最佳体验，建议使用支持压感的手写笔设备
- 墨水渗透笔的效果需要在绘制时停留才能显现，停留时间越长效果越明显
- 应用已移除多线程代码，改为UI线程绘制，提高了稳定性和可维护性
